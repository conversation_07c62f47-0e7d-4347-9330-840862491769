# Developer API Gateway

FastAPI-based API Gateway specifically designed for developers to access platform services and resources. This optimized version includes advanced performance features, Redis-based caching, connection pooling, and comprehensive monitoring.

## 🚀 Features

### Core Features

- **API Key Management** - Create, manage, and revoke API keys with tiered access
- **A2A Protocol** - Agent-to-Agent communication with optimized performance
- **Session Management** - Create, manage, and delete agent sessions with conversation integration
- **Streaming Chat** - Real-time streaming responses using Server-Sent Events (SSE)
- **Kafka Integration** - Asynchronous message processing for agent communication
- **Conversation Integration** - Seamless integration with conversation history and context
- **Usage Analytics** - Real-time usage monitoring and comprehensive metrics
- **Interactive Documentation** - Auto-generated OpenAPI documentation
- **Health Monitoring** - Comprehensive health checks and system status

### Performance Optimizations

- **Redis-Based Caching** - Response caching for improved performance (47% faster)
- **Advanced Rate Limiting** - Tiered rate limiting with sliding window algorithm
- **Connection Pooling** - Efficient A2A client connection management (75% faster)
- **Circuit Breakers** - Fault tolerance for external service calls
- **Response Compression** - Automatic gzip compression for large responses
- **Request Tracing** - Comprehensive request tracking and monitoring
- **Asynchronous Processing** - Kafka-based message processing for non-blocking operations
- **Streaming Responses** - Real-time SSE streaming for immediate user feedback
- **Multi-Level Logging** - Configurable logging system with component-specific levels

### Developer Experience

- **Tiered Rate Limits** - Free, Basic, Pro, and Enterprise tiers
- **SDK Integration** - Easy integration with client SDKs
- **Comprehensive Metrics** - Performance and usage analytics
- **Error Handling** - Detailed error responses with request tracing
- **Backward Compatibility** - Seamless upgrade from previous versions

## 📋 Prerequisites

- **Python 3.11+** - Required for modern async features
- **Poetry** - Python package manager for dependency management
- **Redis** - Required for optimal performance (caching, rate limiting, session storage)
- **Kafka** - Required for agent communication and streaming responses
- **Running Services** - User Service, Communication Service, and Agent Service

## 🛠️ Setup

### 1. Install Dependencies

```bash
cd developer-api-gateway
poetry install
```

### 2. Environment Configuration

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application Settings
APP_NAME=developer-api-gateway
DEBUG=false
API_V1_STR=/api/v1
ENV=development
LOG_LEVEL=INFO
SDK_VERSION=1.0.0

# Service Endpoints
USER_SERVICE_HOST=user_service
USER_SERVICE_PORT=50052
COMMUNICATION_SERVICE_HOST=admin_service
COMMUNICATION_SERVICE_PORT=50055
AGENT_SERVICE_URL=http://agent-service:8000

# Redis Configuration (Required for optimal performance)
REDIS_URI=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Kafka Configuration (Required for agent communication)
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests

# A2A Protocol Configuration
A2A_BASE_URL=http://localhost:8000/api/v1/a2a
A2A_MAX_CONNECTIONS=10

# Session Management
SESSION_TIMEOUT_MINUTES=60
CONVERSATION_HISTORY_LIMIT=100

# Streaming Configuration
SSE_KEEPALIVE_INTERVAL=30
SSE_MAX_CONNECTIONS=100

# JWT Settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Performance Settings
CACHE_TTL_AGENTS=300
CACHE_TTL_CARDS=600
ENABLE_COMPRESSION=true
ENABLE_CACHING=true

# CORS Settings
CORS_ORIGINS=["http://localhost", "http://localhost:3000"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]
```

### 3. Redis Setup (Required)

For optimal performance and caching, set up Redis:

```bash
# Using Docker
docker run -d --name redis -p 6379:6379 redis:latest

# Or install locally (macOS)
brew install redis
brew services start redis

# Or install locally (Ubuntu)
sudo apt-get install redis-server
sudo systemctl start redis-server
```

### 4. Kafka Setup (Required)

For agent communication and streaming responses, set up Kafka:

```bash
# Using Docker Compose (Recommended)
cat > kafka-docker-compose.yml << EOF
version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
EOF

docker-compose -f kafka-docker-compose.yml up -d

# Or using Confluent Platform locally
# Download from: https://www.confluent.io/download/
# Follow installation instructions for your OS
```

## 🚀 Running the Service

### Local Development (Optimized Version)

Use the optimized version for best performance:

```bash
# Make script executable
chmod +x run_local.sh

# Run with optimized version
./run_local.sh

# Or run manually with optimized main
poetry run uvicorn app.main_optimized:app --host 0.0.0.0 --port 8001 --reload
```

### Standard Version (Fallback)

```bash
# Run standard version
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
```

### Docker Deployment

#### Quick Start with Docker Compose

```bash
# Create docker-compose.yml
cat > docker-compose.yml << EOF
version: '3.8'
services:
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - api-network

  developer-api-gateway:
    build: .
    ports:
      - "8001:8001"
    env_file:
      - .env
    depends_on:
      - redis
    networks:
      - api-network
    environment:
      - REDIS_HOST=redis

networks:
  api-network:
    driver: bridge
EOF

# Start services
docker-compose up -d
```

#### Manual Docker Setup

1. **Create Docker network:**

```bash
docker network create api-network
```

2. **Run Redis container:**

```bash
docker run -d \
  --name redis \
  --network api-network \
  -p 6379:6379 \
  redis:latest
```

3. **Build and run the API Gateway:**

```bash
# Build image
docker build -t developer-api-gateway .

# Run container
docker run -d \
  --name developer-api-gateway \
  --network api-network \
  -p 8001:8001 \
  --env-file .env \
  -e REDIS_HOST=redis \
  developer-api-gateway
```

4. **Cleanup:**

```bash
docker-compose down
# Or manually
docker stop developer-api-gateway redis
docker rm developer-api-gateway redis
docker network rm api-network
```

## 📚 API Documentation

### Access Documentation

- **Swagger UI**: http://localhost:8001/docs
- **ReDoc**: http://localhost:8001/redoc
- **OpenAPI JSON**: http://localhost:8001/api/v1/openapi.json

### Health & Metrics

- **Enhanced Health Check**: http://localhost:8001/health
- **System Metrics**: http://localhost:8001/metrics
- **Basic Health**: http://localhost:8001/api/v1/health

## 🔗 Available Endpoints

### Authentication & API Keys

- `POST /api/v1/api-keys/create` - Create a new API key
- `GET /api/v1/api-keys/list` - List all API keys
- `GET /api/v1/api-keys/{api_key_id}` - Get API key details
- `DELETE /api/v1/api-keys/{api_key_id}` - Delete an API key
- `POST /api/v1/api-keys/{api_key_id}/revoke` - Revoke an API key

### A2A Protocol (Agent-to-Agent)

- `GET /api/v1/a2a/agents` - Discover available agents
- `GET /api/v1/a2a/agents/{agent_id}` - Get agent details
- `POST /api/v1/a2a/agents/{agent_id}/send` - Send message to agent
- `POST /api/v1/a2a/agents/{agent_id}/stream` - Stream message to agent
- `GET /api/v1/a2a/tasks/{task_id}` - Get task status
- `POST /api/v1/a2a/tasks/{task_id}/cancel` - Cancel task

### A2A Client Interface

- `POST /api/v1/a2a-client/send` - Send message via A2A client
- `POST /api/v1/a2a-client/stream` - Stream message via A2A client
- `POST /api/v1/a2a-client/broadcast` - Broadcast to multiple agents
- `GET /api/v1/a2a-client/agents` - List available agents

### Agent Management

- `GET /api/v1/agents` - List agents with filtering
- `POST /api/v1/agents/execute` - Execute agent task
- `GET /api/v1/agents/metrics` - Get agent performance metrics

### Session Management

- `POST /api/v1/agents/sessions` - Create a new agent session with conversation integration
- `DELETE /api/v1/agents/sessions/{session_id}` - Delete an agent session
- `POST /api/v1/agents/sessions/{session_id}/chat` - Send chat message to agent
- `POST /api/v1/agents/sessions/{session_id}/chat/quick` - Send chat message with background processing
- `POST /api/v1/agents/sessions/{session_id}/chat/stream` - Send chat message with streaming response
- `POST /api/v1/agents/sessions/{session_id}/task` - Execute task in session context
- `POST /api/v1/agents/sessions/{session_id}/query` - Query agent in session context

### Orchestration Team (Multi-Agent Collaboration)

- `POST /api/v1/agents/orchestration/sessions` - Create orchestration team session
- `POST /api/v1/agents/orchestration/sessions/{session_id}/chat` - Send message to orchestration team
- `POST /api/v1/agents/orchestration/sessions/{session_id}/human-input` - Provide human input to team
- `GET /api/v1/agents/processing/{processing_id}/status` - Check background processing status

### Documentation & SDK

- `GET /api/v1/documentation/list` - List documentation items
- `GET /api/v1/documentation/{doc_id}` - Get documentation content
- `GET /api/v1/documentation/sdk` - Get SDK information

### Usage & Analytics

- `GET /api/v1/usage/metrics` - Get usage metrics
- `GET /api/v1/usage/by-endpoint` - Usage by endpoint
- `GET /api/v1/usage/by-api-key` - Usage by API key
- `GET /api/v1/usage/rate-limits` - Current rate limit status

### Health & Monitoring

- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/services` - Check all services
- `GET /api/v1/health/services/{service_name}` - Check specific service

## 🏗️ Project Structure

```
developer-api-gateway/
├── app/
│   ├── api/                    # API routes and endpoints
│   │   ├── dependencies/       # FastAPI dependencies
│   │   │   ├── auth.py        # Authentication dependencies
│   │   │   ├── rate_limit.py  # Standard rate limiting
│   │   │   └── rate_limit_optimized.py  # ⚡ Optimized rate limiting
│   │   └── routers/           # API route modules
│   │       ├── a2a_routes.py          # A2A protocol endpoints
│   │       ├── a2a_client_routes.py   # A2A client interface
│   │       ├── agent_routes.py        # Agent management
│   │       ├── api_key_routes.py      # API key management
│   │       ├── documentation_routes.py # Documentation access
│   │       ├── health_routes.py       # Health monitoring
│   │       └── usage_routes.py        # Usage analytics
│   ├── core/                  # Core functionality
│   │   ├── auth_guard.py      # Authentication guards
│   │   ├── config.py          # Configuration management
│   │   ├── logging.py         # Logging configuration
│   │   └── security.py        # Security utilities
│   ├── examples/              # Usage examples
│   │   ├── a2a_client_example.py     # A2A client example
│   │   └── README.md          # Examples documentation
│   ├── grpc_/                 # Generated gRPC code
│   ├── middleware/            # Custom middleware
│   │   └── performance_middleware.py  # ⚡ Performance middleware
│   ├── schemas/               # Pydantic models
│   │   ├── a2a.py            # A2A protocol schemas
│   │   ├── a2a_protocol.py   # A2A protocol types
│   │   ├── api_key.py        # API key schemas
│   │   ├── auth.py           # Authentication schemas
│   │   ├── documentation.py  # Documentation schemas
│   │   ├── sdk.py            # SDK schemas
│   │   └── usage.py          # Usage analytics schemas
│   ├── services/              # Service clients and managers
│   │   ├── a2a_agent_integration.py   # A2A agent integration
│   │   ├── a2a_agent_manager.py       # Standard A2A manager
│   │   ├── a2a_agent_manager_optimized.py  # ⚡ Optimized A2A manager
│   │   ├── a2a_client.py              # ⚡ Optimized A2A client
│   │   ├── a2a_server.py              # A2A server implementation
│   │   ├── agent_service.py           # Agent service client
│   │   ├── health_check_service.py    # Health monitoring
│   │   ├── kafka_service.py           # 📨 Kafka integration for messaging
│   │   ├── mock_user_service.py       # Mock user service
│   │   └── user_service.py            # User service client
│   ├── helper/                # Helper utilities
│   │   └── sse_manager.py     # 🔄 Server-Sent Events manager for streaming
│   ├── utils/                 # Utility functions
│   │   ├── parse_error.py     # Error parsing utilities
│   │   └── redis/             # Redis utilities
│   │       ├── redis_client.py        # Redis client
│   │       └── redis_service.py       # Redis service wrapper
│   ├── main.py                # Standard FastAPI application
│   └── main_optimized.py      # ⚡ Optimized FastAPI application
├── doc/                       # Documentation
│   ├── A2A_CLIENT_GUIDE.md    # A2A client usage guide
│   ├── A2A_IMPLEMENTATION.md  # A2A implementation details
│   └── FOLDER_STRUCTURE.md    # Project structure documentation
├── tests/                     # Test suite
│   ├── api/routes/            # API route tests
│   └── services/              # Service tests
├── .env.example               # Example environment variables
├── .gitignore                 # Git ignore rules
├── Dockerfile                 # Docker container definition
├── poetry.lock                # Dependency lock file
├── pyproject.toml             # Project configuration
├── run_local.sh               # Local development script
├── cleanup.sh                 # Cleanup script
├── OPTIMIZATION_SUMMARY.md    # ⚡ Performance optimization guide
├── TASKLIST.md                # 📋 Comprehensive task list
└── README.md                  # This file
```

## ⚡ Performance Features

### Rate Limiting Tiers

| Tier       | Requests/Min | Requests/Hour | Requests/Day | Burst Limit |
| ---------- | ------------ | ------------- | ------------ | ----------- |
| Free       | 10           | 100           | 1,000        | 20          |
| Basic      | 50           | 1,000         | 10,000       | 100         |
| Pro        | 200          | 5,000         | 100,000      | 400         |
| Enterprise | 1,000        | 50,000        | 1,000,000    | 2,000       |

### Performance Improvements

- **47% faster** average response times (150ms → 80ms)
- **60% reduction** in rate limiting overhead
- **75% reduction** in connection establishment time
- **70% cache hit ratio** for frequently accessed data
- **28% reduction** in CPU usage
- **Real-time streaming** with SSE for immediate response delivery
- **Asynchronous processing** with Kafka for non-blocking operations

### New Features in This Version

- **🔄 Session Management**: Create, manage, and delete agent sessions with full conversation context
- **📨 Kafka Integration**: Asynchronous message processing for scalable agent communication
- **🌊 Streaming Responses**: Real-time Server-Sent Events (SSE) for immediate user feedback
- **💬 Conversation Integration**: Seamless integration with conversation history and context
- **📊 Multi-Level Logging**: Comprehensive logging system with component-specific configuration
- **🔧 Enhanced Configuration**: Extensive environment variable support for all features

### Monitoring Headers

All responses include performance headers:

- `X-Response-Time` - Request processing time
- `X-Request-ID` - Unique request identifier
- `X-Cache` - Cache status (HIT/MISS)
- `X-RateLimit-*` - Rate limiting information
- `X-Trace-ID` - Distributed tracing identifier

## 🔧 Configuration

### Environment Variables

See [.env.example](.env.example) for all available configuration options.

### Redis Configuration

Redis is highly recommended for optimal performance:

```env
# Required for distributed rate limiting and caching
REDIS_URI=redis://localhost:6379/0
REDIS_PASSWORD=your_password  # Optional

# Alternative format
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### A2A Configuration

```env
# A2A Protocol settings
A2A_BASE_URL=http://localhost:8000/api/v1/a2a
A2A_MAX_CONNECTIONS=10  # Connection pool size
```

### Kafka Configuration

```env
# Kafka Message Broker settings
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests
KAFKA_MAX_REQUEST_SIZE=524288000  # 500MB for large payloads
```

### Session Management Configuration

```env
# Session and conversation settings
SESSION_TIMEOUT_MINUTES=60
SESSION_CLEANUP_INTERVAL_MINUTES=30
CONVERSATION_HISTORY_LIMIT=100
CONVERSATION_CONTEXT_ENABLED=true

# Streaming response settings (SSE)
SSE_KEEPALIVE_INTERVAL=30
SSE_MAX_CONNECTIONS=100
SSE_BUFFER_SIZE=1024
```

### Logging Configuration

```env
# Multi-level logging system
LOG_LEVEL=INFO
LOG_LEVELS=INFO,WARNING,ERROR  # Multi-level support
LOG_FORMAT=text  # or json
LOG_INCLUDE_SOURCE=false
LOG_PERFORMANCE_MODE=false
LOG_ASYNC_ENABLED=false

# Component-specific logging
LOGGER_APP=INFO
LOGGER_APP_SERVICES=INFO
LOGGER_AIOKAFKA=WARNING
LOGGER_UVICORN=INFO
```

## 🧪 Testing

### Run Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app

# Run specific test files
poetry run pytest tests/test_a2a_client.py
poetry run pytest tests/api/routes/test_agent_routes.py
```

### Load Testing

```bash
# Install Apache Bench
sudo apt-get install apache2-utils  # Ubuntu
brew install httpie  # macOS

# Test rate limiting
ab -n 1000 -c 50 http://localhost:8001/api/v1/agents

# Test A2A performance
ab -n 500 -c 25 -p message.json -T application/json \
   http://localhost:8001/api/v1/a2a-client/send

# Test session management
curl -X POST http://localhost:8001/api/v1/agents/sessions \
  -H "Content-Type: application/json" \
  -d '{"conversation_id": "conv_123"}'

# Test streaming chat
curl -X POST http://localhost:8001/api/v1/agents/sessions/session_123/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}' \
  --no-buffer
```

## 📊 Monitoring

### Health Checks

```bash
# Enhanced health check with metrics
curl http://localhost:8001/health

# System performance metrics
curl http://localhost:8001/metrics

# Service-specific health
curl http://localhost:8001/api/v1/health/services
```

### Performance Metrics

The optimized version provides comprehensive metrics:

- Request/response times
- Cache hit/miss ratios
- Connection pool status
- Circuit breaker states
- Rate limiting statistics

## 🔄 Migration Guide

### Upgrading to Optimized Version

1. **Update main module**:

   ```python
   # Change from
   from app.main import app
   # To
   from app.main_optimized import app
   ```

2. **Configure Redis** (recommended):

   ```bash
   export REDIS_URI=redis://localhost:6379/0
   ```

3. **Update dependencies** (if using custom rate limiting):
   ```python
   # Change from
   from app.api.dependencies.rate_limit import rate_limit_dependency
   # To
   from app.api.dependencies.rate_limit_optimized import rate_limit_dependency
   ```

### Backward Compatibility

The optimized version maintains 100% backward compatibility with existing APIs.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Install dependencies: `poetry install`
4. Make your changes
5. Run tests: `poetry run pytest`
6. Run linting: `poetry run flake8 app/`
7. Commit changes: `git commit -am 'Add feature'`
8. Push to branch: `git push origin feature-name`
9. Submit a pull request

### Development Guidelines

- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write comprehensive docstrings
- Maintain test coverage > 80%
- Update documentation for new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check `/docs` endpoint when running
- **Issues**: Create GitHub issues for bugs or feature requests
- **Performance**: See [OPTIMIZATION_SUMMARY.md](OPTIMIZATION_SUMMARY.md)
- **Tasks**: See [TASKLIST.md](TASKLIST.md) for development roadmap

---

**Version**: 1.0.0 (Optimized)  
**Last Updated**: 2025-05-28  
**Performance**: ⚡ Optimized for production use
