"""
Test script to demonstrate event handling functionality with actual event data structure.

This script shows how the updated Kafka service and event handler
can process all the different types of SSE events using the real event data format.
"""

import async<PERSON>
import json
from unittest.mock import AsyncMock, MagicMock

from app.helper.sse_manager import <PERSON><PERSON><PERSON>anager
from app.services.event_handler import <PERSON><PERSON><PERSON><PERSON>
from app.shared.constants import SSEEventType


async def test_event_handling():
    """Test the event handling functionality with actual event data structure."""
    print("Testing Event Handling System with Actual Event Data")
    print("=" * 60)

    # Create mock SSE manager
    sse_manager = MagicMock(spec=SseManager)

    # Create event handler
    event_handler = EventHandler(sse_manager)

    # Test events using the actual data structure
    test_events = [
        # Session management events
        {
            "event_type": SSEEventType.SESSION_INITIALIZED.value,
            "session_id": "test_session_123",
            "status": "initialized",
        },
        # Message streaming events
        {"event_type": SSEEventType.MESSAGE_STREAM_STARTED.value, "session_id": "test_session_123"},
        {
            "event_type": SSEEventType.MESSAGE_STREAMING.value,
            "session_id": "test_session_123",
            "agent_response": {
                "content": "Hello, this is a streaming response...",
                "message_type": "streaming_chunk",
                "source": "assistant",
                "metadata": {"chunk_index": 1},
            },
            "content_type": "text/plain",
        },
        {"event_type": SSEEventType.MESSAGE_END.value, "session_id": "test_session_123"},
        {
            "event_type": SSEEventType.MESSAGE_RESPONSE.value,
            "session_id": "test_session_123",
            "agent_response": "Complete response message",
            "content_type": "text/plain",
        },
        # MCP (Model Context Protocol) events - ACTUAL FORMAT
        {
            "run_id": "mcp_run_456",
            "request_id": "mcp_req_789",
            "message": "Agent initiated image_generator mcp tool execution.",
            "tool_name": "image_generator",
            "success": True,
            "final": True,
            "event_type": SSEEventType.MCP_EXECUTION_STARTED.value,
            "session_id": "test_session_123",
        },
        {
            "run_id": "mcp_run_456",
            "request_id": "mcp_req_789",
            "message": "MCP tool execution completed successfully.",
            "tool_name": "image_generator",
            "success": True,
            "final": True,
            "event_type": SSEEventType.MCP_EXECUTION_ENDED.value,
            "session_id": "test_session_123",
        },
        {
            "run_id": "mcp_run_456",
            "request_id": None,
            "tool_name": "image_generator",
            "message": "mcp tool execution error: Connection timeout",
            "success": False,
            "final": True,
            "event_type": SSEEventType.MCP_EXECUTION_FAILED.value,
            "session_id": "test_session_123",
        },
        # Workflow execution events - ACTUAL FORMAT
        {
            "run_id": "workflow_run_789",
            "correlation_id": "corr_101112",
            "message": "Agent initiated data_analysis workflow execution.",
            "workflow_id": "data_analysis_workflow",
            "success": True,
            "final": True,
            "event_type": SSEEventType.WORKFLOW_EXECUTION_STARTED.value,
            "session_id": "test_session_123",
        },
        {
            "run_id": "workflow_run_789",
            "correlation_id": None,
            "workflow_id": "data_analysis_workflow",
            "message": "Workflow execution error: Invalid input data",
            "success": False,
            "final": True,
            "event_type": SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
            "session_id": "test_session_123",
        },
        # Knowledge fetch events - ACTUAL FORMAT
        {
            "run_id": "knowledge_run_131415",
            "message": "Agent initiated knowledge content retrieval.",
            "success": True,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_STARTED.value,
            "session_id": "test_session_123",
        },
        {
            "run_id": "knowledge_run_131415",
            "message": "Agent completed knowledge content retrieval.",
            "success": True,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value,
            "session_id": "test_session_123",
        },
        {
            "run_id": "knowledge_run_131415",
            "message": "Agent failed to retrieve knowledge content: Database connection failed",
            "success": False,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_FAILED.value,
            "session_id": "test_session_123",
        },
    ]

    # Process each test event
    for i, event in enumerate(test_events, 1):
        print(f"\n{i}. Testing {event['event_type']} event:")
        print(f"   Event data: {json.dumps(event, indent=2)}")

        # Handle the event
        result = await event_handler.handle_event(event)

        if result:
            print(f"   ✅ Event handled successfully")
        else:
            print(f"   ❌ Event handling failed")

    print(f"\n" + "=" * 60)
    print("Event handling test completed!")

    # Print summary of SSE manager calls
    print(f"\nSSE Manager Method Calls Summary:")
    print(f"Total method calls: {len(sse_manager.method_calls)}")

    for call in sse_manager.method_calls:
        method_name = call[0]
        print(f"  - {method_name}")


def test_actual_event_data_structure():
    """Test that the event handler works with the actual event data structure."""
    print("\nTesting Actual Event Data Structure Compatibility")
    print("=" * 50)

    # Test the actual event structures provided
    actual_events = [
        # Workflow execution started
        {
            "run_id": "workflow_123",
            "correlation_id": "corr_456",
            "message": "Agent initiated data_processing workflow execution.",
            "workflow_id": "data_processing",
            "success": True,
            "final": True,
            "event_type": SSEEventType.WORKFLOW_EXECUTION_STARTED.value,
        },
        # Workflow execution failed
        {
            "run_id": "workflow_123",
            "correlation_id": None,
            "workflow_id": "data_processing",
            "message": "Workflow execution error: Invalid configuration",
            "success": False,
            "final": True,
            "event_type": SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
        },
        # MCP execution started
        {
            "run_id": "mcp_789",
            "request_id": "req_101112",
            "message": "Agent initiated text_analyzer mcp tool execution.",
            "tool_name": "text_analyzer",
            "success": True,
            "final": True,
            "event_type": SSEEventType.MCP_EXECUTION_STARTED.value,
        },
        # MCP execution failed
        {
            "run_id": "mcp_789",
            "request_id": None,
            "tool_name": "text_analyzer",
            "message": "mcp tool execution error: API rate limit exceeded",
            "success": False,
            "final": True,
            "event_type": SSEEventType.MCP_EXECUTION_FAILED.value,
        },
        # Knowledge fetch events
        {
            "run_id": "knowledge_131415",
            "message": "Agent initiated knowledge content retrieval.",
            "success": True,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_STARTED.value,
        },
        {
            "run_id": "knowledge_131415",
            "message": "Agent completed knowledge content retrieval.",
            "success": True,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value,
        },
        {
            "run_id": "knowledge_131415",
            "message": "Agent failed to retrieve knowledge content: Network timeout",
            "success": False,
            "final": True,
            "event_type": SSEEventType.KNOWLEDGE_FETCH_FAILED.value,
        },
    ]

    print(f"Testing {len(actual_events)} actual event structures:")

    for i, event in enumerate(actual_events, 1):
        event_type = event.get("event_type", "unknown")
        run_id = event.get("run_id", "N/A")
        success = event.get("success", "N/A")

        print(f"  {i}. {event_type}")
        print(f"     - run_id: {run_id}")
        print(f"     - success: {success}")
        print(f"     - message: {event.get('message', 'N/A')[:50]}...")

    print("\nAll actual event structures are compatible! ✅")


if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_event_handling())
    test_actual_event_data_structure()
