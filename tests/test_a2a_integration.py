#!/usr/bin/env python3
"""
Test A2A Protocol Integration

This script tests the A2A protocol integration to ensure agents are properly
configured and accessible via the A2A protocol.
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any, List

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import httpx
import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.services.agent_service import AgentServiceClient
from app.services.a2a_server import MultiAgentA2AServer


class A2AIntegrationTester:
    """Test A2A protocol integration."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url, timeout=30.0)
        
    async def test_a2a_info(self) -> Dict[str, Any]:
        """Test A2A protocol information endpoint."""
        try:
            response = await self.client.get("/api/v1/a2a/")
            response.raise_for_status()
            data = response.json()
            
            print("✅ A2A Info endpoint working")
            print(f"   Registered agents: {len(data.get('registered_agents', []))}")
            print(f"   Supported operations: {len(data.get('supported_operations', {}))}")
            
            return data
        except Exception as e:
            print(f"❌ A2A Info endpoint failed: {e}")
            return {}
    
    async def test_agent_discovery(self) -> List[Dict[str, Any]]:
        """Test agent discovery endpoint."""
        try:
            response = await self.client.get("/api/v1/a2a/agents")
            response.raise_for_status()
            data = response.json()
            
            agents = data.get('agents', [])
            print(f"✅ Agent discovery working - found {len(agents)} agents")
            
            for agent in agents[:3]:  # Show first 3 agents
                print(f"   - {agent.get('name', 'Unknown')} ({agent.get('id', 'No ID')})")
            
            return agents
        except Exception as e:
            print(f"❌ Agent discovery failed: {e}")
            return []
    
    async def test_agent_card(self, agent_id: str) -> Dict[str, Any]:
        """Test agent card endpoint."""
        try:
            response = await self.client.get(f"/api/v1/a2a/agents/{agent_id}")
            response.raise_for_status()
            data = response.json()
            
            agent_card = data.get('agent_card', {})
            print(f"✅ Agent card for {agent_id} retrieved")
            print(f"   Name: {agent_card.get('name', 'Unknown')}")
            print(f"   Description: {agent_card.get('description', 'No description')[:50]}...")
            print(f"   Skills: {len(agent_card.get('skills', []))}")
            
            return agent_card
        except Exception as e:
            print(f"❌ Agent card for {agent_id} failed: {e}")
            return {}
    
    async def test_well_known_agent_card(self, agent_id: str) -> Dict[str, Any]:
        """Test well-known agent card endpoint."""
        try:
            response = await self.client.get(f"/api/v1/a2a/agents/{agent_id}/.well-known/agent.json")
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ Well-known agent card for {agent_id} retrieved")
            print(f"   URL: {data.get('url', 'No URL')}")
            print(f"   Version: {data.get('version', 'No version')}")
            print(f"   Capabilities: {data.get('capabilities', {})}")
            
            return data
        except Exception as e:
            print(f"❌ Well-known agent card for {agent_id} failed: {e}")
            return {}
    
    async def test_a2a_message_send(self, agent_id: str, message: str = "Hello, how can you help me?") -> Dict[str, Any]:
        """Test A2A message/send method."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": "test-001",
                "method": "message/send",
                "params": {
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "kind": "text",
                                "text": message
                            }
                        ],
                        "messageId": "test-msg-001"
                    }
                }
            }
            
            response = await self.client.post(
                f"/api/v1/a2a/agents/{agent_id}/a2a",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ A2A message/send for {agent_id} successful")
            print(f"   Response ID: {data.get('id', 'No ID')}")
            print(f"   Result type: {type(data.get('result', {}))}")
            
            return data
        except Exception as e:
            print(f"❌ A2A message/send for {agent_id} failed: {e}")
            return {}
    
    async def test_a2a_management_status(self, agent_id: str) -> Dict[str, Any]:
        """Test A2A management status endpoint."""
        try:
            response = await self.client.get(f"/api/v1/a2a/management/agents/{agent_id}/status")
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ A2A management status for {agent_id} retrieved")
            print(f"   A2A Enabled: {data.get('is_a2a_enabled', False)}")
            print(f"   Capabilities: {len(data.get('capabilities', {}))}")
            print(f"   Skills: {len(data.get('skills', []))}")
            
            return data
        except Exception as e:
            print(f"❌ A2A management status for {agent_id} failed: {e}")
            return {}
    
    async def test_a2a_management_list(self) -> Dict[str, Any]:
        """Test A2A management list endpoint."""
        try:
            response = await self.client.get("/api/v1/a2a/management/agents")
            response.raise_for_status()
            data = response.json()
            
            agents = data.get('agents', [])
            print(f"✅ A2A management list retrieved - {len(agents)} agents")
            
            for agent in agents[:3]:  # Show first 3 agents
                print(f"   - {agent.get('name', 'Unknown')} (Skills: {agent.get('skills_count', 0)})")
            
            return data
        except Exception as e:
            print(f"❌ A2A management list failed: {e}")
            return {}
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive A2A integration test."""
        print("🚀 Starting A2A Protocol Integration Test\n")
        
        results = {
            "a2a_info": {},
            "agent_discovery": [],
            "agent_tests": [],
            "management_tests": {},
            "overall_status": "unknown"
        }
        
        try:
            # Test A2A info
            print("1. Testing A2A Protocol Information...")
            results["a2a_info"] = await self.test_a2a_info()
            print()
            
            # Test agent discovery
            print("2. Testing Agent Discovery...")
            agents = await self.test_agent_discovery()
            results["agent_discovery"] = agents
            print()
            
            # Test management endpoints
            print("3. Testing A2A Management...")
            results["management_tests"] = await self.test_a2a_management_list()
            print()
            
            # Test individual agents
            if agents:
                print("4. Testing Individual Agents...")
                test_agent = agents[0]
                agent_id = test_agent.get('id')
                
                if agent_id:
                    agent_results = {
                        "agent_id": agent_id,
                        "agent_card": await self.test_agent_card(agent_id),
                        "well_known_card": await self.test_well_known_agent_card(agent_id),
                        "management_status": await self.test_a2a_management_status(agent_id),
                        "message_send": await self.test_a2a_message_send(agent_id)
                    }
                    results["agent_tests"].append(agent_results)
                print()
            
            # Determine overall status
            success_count = sum([
                1 if results["a2a_info"] else 0,
                1 if results["agent_discovery"] else 0,
                1 if results["management_tests"] else 0,
                1 if results["agent_tests"] else 0
            ])
            
            if success_count >= 3:
                results["overall_status"] = "success"
                print("🎉 A2A Integration Test: SUCCESS")
            elif success_count >= 2:
                results["overall_status"] = "partial"
                print("⚠️  A2A Integration Test: PARTIAL SUCCESS")
            else:
                results["overall_status"] = "failure"
                print("❌ A2A Integration Test: FAILURE")
            
        except Exception as e:
            print(f"💥 A2A Integration Test failed with exception: {e}")
            results["overall_status"] = "error"
            results["error"] = str(e)
        
        return results
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test A2A Protocol Integration")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL for the API")
    parser.add_argument("--agent-id", help="Test specific agent by ID")
    parser.add_argument("--message", default="Hello, how can you help me?", help="Test message to send")
    parser.add_argument("--output", help="Save results to JSON file")
    
    args = parser.parse_args()
    
    tester = A2AIntegrationTester(base_url=args.base_url)
    
    try:
        if args.agent_id:
            # Test specific agent
            print(f"Testing specific agent: {args.agent_id}")
            await tester.test_agent_card(args.agent_id)
            await tester.test_well_known_agent_card(args.agent_id)
            await tester.test_a2a_management_status(args.agent_id)
            await tester.test_a2a_message_send(args.agent_id, args.message)
        else:
            # Run comprehensive test
            results = await tester.run_comprehensive_test()
            
            if args.output:
                with open(args.output, 'w') as f:
                    json.dump(results, f, indent=2)
                print(f"\nResults saved to {args.output}")
    
    finally:
        await tester.close()


# Pytest tests for CI/CD
@pytest.mark.asyncio
async def test_a2a_info_endpoint():
    """Test A2A info endpoint."""
    with TestClient(app) as client:
        response = client.get("/api/v1/a2a/")
        assert response.status_code == 200
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert "endpoints" in data


@pytest.mark.asyncio
async def test_agent_discovery_endpoint():
    """Test agent discovery endpoint."""
    with TestClient(app) as client:
        response = client.get("/api/v1/a2a/agents")
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
        assert "agents" in data


@pytest.mark.asyncio
async def test_a2a_management_list():
    """Test A2A management list endpoint."""
    with TestClient(app) as client:
        response = client.get("/api/v1/a2a/management/agents")
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
        assert "agents" in data


if __name__ == "__main__":
    asyncio.run(main())
