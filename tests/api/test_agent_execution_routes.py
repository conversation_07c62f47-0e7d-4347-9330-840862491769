"""
Tests for agent execution routes.

This module contains tests for the agent execution API endpoints.
"""

import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from app.main import app
from app.schemas.agent import AgentExecutionRequest


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_api_key():
    """Mock API key for testing."""
    return "test-api-************************************"


@pytest.fixture
def mock_user():
    """Mock user for testing."""
    return {
        "id": "test-user-123",
        "email": "<EMAIL>",
        "is_active": True,
    }


@pytest.fixture
def sample_execution_request():
    """Sample agent execution request."""
    return {
        "query": "What is the capital of France?",
        "metadata": {"priority": "high"}
    }


class TestAgentExecutionRoutes:
    """Test class for agent execution routes."""

    @patch("app.api.dependencies.auth.get_api_key")
    @patch("app.api.dependencies.auth.get_current_active_user")
    @patch("app.services.agent_service.AgentServiceClient.get_agent")
    @patch("app.services.kafka_service.kafka_service.create_agent_session")
    @patch("app.services.kafka_service.kafka_service.execute_agent_task")
    def test_execute_agent_task_success(
        self,
        mock_execute_task,
        mock_create_session,
        mock_get_agent,
        mock_get_user,
        mock_get_api_key,
        client,
        mock_api_key,
        mock_user,
        sample_execution_request,
    ):
        """Test successful agent task execution."""
        # Setup mocks
        mock_get_api_key.return_value = mock_api_key
        mock_get_user.return_value = mock_user
        mock_get_agent.return_value = {
            "id": "agent-1",
            "name": "Test Agent",
            "status": "online"
        }
        mock_create_session.return_value = "session-123"
        mock_execute_task.return_value = {
            "task_id": "task-456",
            "status": {"state": "submitted"},
            "result": None,
            "error": None,
        }

        # Make request
        response = client.post(
            "/api/v1/agents/agent-1/execute",
            json=sample_execution_request,
            headers={"X-API-Key": mock_api_key}
        )

        # Assertions
        assert response.status_code == 202
        data = response.json()
        assert data["task_id"] == "task-456"
        assert data["session_id"] == "session-123"
        assert data["status"] == "submitted"
        assert data["agent_id"] == "agent-1"
        assert data["result"] is None
        assert data["error"] is None
        assert "execution_time_ms" in data

        # Verify mocks were called
        mock_get_agent.assert_called_once_with("agent-1")
        mock_create_session.assert_called_once_with("agent-1", "test-user-123")
        mock_execute_task.assert_called_once()

    @patch("app.api.dependencies.auth.get_api_key")
    @patch("app.api.dependencies.auth.get_current_active_user")
    @patch("app.services.agent_service.AgentServiceClient.get_agent")
    def test_execute_agent_task_agent_not_found(
        self,
        mock_get_agent,
        mock_get_user,
        mock_get_api_key,
        client,
        mock_api_key,
        mock_user,
        sample_execution_request,
    ):
        """Test agent task execution with non-existent agent."""
        # Setup mocks
        mock_get_api_key.return_value = mock_api_key
        mock_get_user.return_value = mock_user
        mock_get_agent.return_value = None

        # Make request
        response = client.post(
            "/api/v1/agents/invalid-agent/execute",
            json=sample_execution_request,
            headers={"X-API-Key": mock_api_key}
        )

        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert "Agent with ID 'invalid-agent' not found" in data["detail"]

    @patch("app.api.dependencies.auth.get_api_key")
    @patch("app.api.dependencies.auth.get_current_active_user")
    def test_execute_agent_task_invalid_request(
        self,
        mock_get_user,
        mock_get_api_key,
        client,
        mock_api_key,
        mock_user,
    ):
        """Test agent task execution with invalid request data."""
        # Setup mocks
        mock_get_api_key.return_value = mock_api_key
        mock_get_user.return_value = mock_user

        # Make request with empty query
        response = client.post(
            "/api/v1/agents/agent-1/execute",
            json={"query": ""},  # Empty query should fail validation
            headers={"X-API-Key": mock_api_key}
        )

        # Assertions
        assert response.status_code == 422

    @patch("app.api.dependencies.auth.get_api_key")
    @patch("app.api.dependencies.auth.get_current_active_user")
    def test_get_task_status_success(
        self,
        mock_get_user,
        mock_get_api_key,
        client,
        mock_api_key,
        mock_user,
    ):
        """Test successful task status retrieval."""
        # Setup mocks
        mock_get_api_key.return_value = mock_api_key
        mock_get_user.return_value = mock_user

        # Make request
        response = client.get(
            "/api/v1/agents/tasks/task-123/status",
            headers={"X-API-Key": mock_api_key}
        )

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == "task-123"
        assert data["status"] == "completed"  # Mock status
        assert data["progress"] == 100.0

    def test_execute_agent_task_no_api_key(self, client, sample_execution_request):
        """Test agent task execution without API key."""
        response = client.post(
            "/api/v1/agents/agent-1/execute",
            json=sample_execution_request
        )

        assert response.status_code == 401

    def test_get_task_status_no_api_key(self, client):
        """Test task status retrieval without API key."""
        response = client.get("/api/v1/agents/tasks/task-123/status")

        assert response.status_code == 401


if __name__ == "__main__":
    pytest.main([__file__])
