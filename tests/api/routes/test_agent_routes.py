"""
Tests for the agent routes.

This module contains tests for the agent routes in the API gateway.
"""

import json
import uuid
import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.api.routers.agent_routes import router
from app.services.kafka_service import KafkaService
from app.services.agent_service import AgentServiceClient


# Create a test app
app = FastAPI()
app.include_router(router)


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI app.
    """
    return TestClient(app)


@pytest.fixture
def mock_kafka_service():
    """
    Create a mock KafkaService.
    """
    with patch("app.api.routers.agent_routes.kafka_service") as mock:
        # Mock the create_agent_session method
        mock.create_agent_session = AsyncMock(return_value="test-session-id")

        # Mock the send_chat_message method
        mock.send_chat_message = AsyncMock(
            return_value={"agent_response": {"content": "Test response"}, "task_id": "test-task-id"}
        )

        # Mock the execute_agent_task method
        mock.execute_agent_task = AsyncMock(
            return_value={
                "task_id": "test-task-id",
                "status": {"state": "completed"},
                "result": {"output": "Test result"},
            }
        )

        yield mock


@pytest.fixture
def mock_agent_service():
    """
    Create a mock AgentServiceClient.
    """
    with patch("app.api.routers.agent_routes.agent_service") as mock:
        # Mock the get_agent method
        mock.get_agent = AsyncMock(
            return_value={
                "id": "test-agent-id",
                "name": "Test Agent",
                "description": "A test agent",
                "capabilities": ["text_chat", "question_answering"],
            }
        )

        yield mock


@pytest.fixture
def mock_auth():
    """
    Mock the authentication dependencies.
    """
    with (
        patch("app.api.routers.agent_routes.get_api_key") as mock_api_key,
        patch("app.api.routers.agent_routes.get_current_active_user") as mock_user,
        patch("app.api.routers.agent_routes.rate_limit") as mock_rate_limit,
    ):

        # Mock the API key dependency
        mock_api_key.return_value = "test-api-key"

        # Mock the user dependency
        mock_user.return_value = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "is_active": True,
        }

        # Mock the rate limit dependency
        mock_rate_limit_instance = MagicMock()
        mock_rate_limit_instance.return_value = None
        mock_rate_limit.return_value = mock_rate_limit_instance

        yield


@pytest.mark.asyncio
async def test_create_session(client, mock_kafka_service, mock_auth):
    """
    Test creating a session.
    """
    # Define the request data
    request_data = {"agent_id": "test-agent-id", "user_id": "test-user-id"}

    # Send the request
    response = client.post("/api/v1/agents/sessions", json=request_data)

    # Check the response
    assert response.status_code == 200
    assert response.json() == {"session_id": "test-session-id"}

    # Check that the Kafka service was called correctly
    mock_kafka_service.create_agent_session.assert_called_once_with("test-agent-id", "test-user-id")


@pytest.mark.asyncio
async def test_send_chat_message(client, mock_kafka_service, mock_auth):
    """
    Test sending a chat message.
    """
    # Define the request data
    request_data = {"message": "Hello, agent!"}

    # Send the request
    response = client.post("/api/v1/agents/sessions/test-session-id/chat", json=request_data)

    # Check the response
    assert response.status_code == 200
    assert response.json() == {"message": "Test response", "task_id": "test-task-id"}

    # Check that the Kafka service was called correctly
    mock_kafka_service.send_chat_message.assert_called_once_with("test-session-id", "Hello, agent!")


@pytest.mark.asyncio
async def test_execute_task(client, mock_kafka_service, mock_auth):
    """
    Test executing a task.
    """
    # Define the request data
    request_data = {
        "task_type": "question_answering",
        "task_input": {"question": "What is the capital of France?"},
    }

    # Send the request
    response = client.post(
        "/api/v1/agents/test-agent-id/sessions/test-session-id/tasks", json=request_data
    )

    # Check the response
    assert response.status_code == 200
    assert response.json() == {
        "task_id": "test-task-id",
        "status": "completed",
        "result": {"output": "Test result"},
    }

    # Check that the Kafka service was called correctly
    mock_kafka_service.execute_agent_task.assert_called_once()
    call_args = mock_kafka_service.execute_agent_task.call_args[0]
    assert call_args[0] == "test-agent-id"
    assert call_args[1] == "test-session-id"
    assert call_args[2]["type"] == "question_answering"
    assert call_args[2]["input"] == {"question": "What is the capital of France?"}


@pytest.mark.asyncio
async def test_get_agent_a2a_status(client, mock_agent_service, mock_auth):
    """
    Test getting the A2A status of an agent.
    """
    # Send the request
    response = client.get("/api/v1/agents/test-agent-id/a2a/status")

    # Check the response
    assert response.status_code == 200
    assert response.json()["agent_id"] == "test-agent-id"
    assert response.json()["a2a_enabled"] is True
    assert "text_chat" in response.json()["capabilities"]

    # Check that the agent service was called correctly
    mock_agent_service.get_agent.assert_called_once_with("test-agent-id")


@pytest.mark.asyncio
async def test_create_a2a_task(client, mock_kafka_service, mock_auth):
    """
    Test creating an A2A task.
    """
    # Define the request data
    request_data = {
        "task_type": "question_answering",
        "task_input": {"question": "What is the capital of France?"},
        "user_id": "test-user-id",
    }

    # Send the request
    response = client.post("/api/v1/agents/test-agent-id/a2a/tasks", json=request_data)

    # Check the response
    assert response.status_code == 200
    assert "task_id" in response.json()
    assert "session_id" in response.json()
    assert response.json()["status"] == "submitted"
