#!/usr/bin/env python3
"""
Test script for the updated agent routes.

This script demonstrates how to use the new agent query functionality
and enhanced chat features.
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class AgentAPIClient:
    """Client for testing the agent API routes."""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url
        self.api_key = api_key or "test-api-key"
        self.headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def create_session(self, agent_id: str, user_id: str = None) -> str:
        """Create a new agent session."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/agents/sessions",
                headers=self.headers,
                json={
                    "agent_id": agent_id,
                    "user_id": user_id
                }
            )
            response.raise_for_status()
            data = response.json()
            return data["session_id"]
    
    async def query_agent(
        self, 
        agent_id: str, 
        query: str, 
        user_id: str = None,
        organization_id: str = None,
        variables: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Query an agent directly."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/agents/{agent_id}/query",
                headers=self.headers,
                json={
                    "query": query,
                    "user_id": user_id,
                    "organization_id": organization_id,
                    "variables": variables or {}
                }
            )
            response.raise_for_status()
            return response.json()
    
    async def send_chat_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """Send a chat message to an agent session."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/agents/sessions/{session_id}/chat",
                headers=self.headers,
                json={"message": message}
            )
            response.raise_for_status()
            return response.json()
    
    async def send_enhanced_chat_message(
        self, 
        session_id: str, 
        chat_context: list
    ) -> Dict[str, Any]:
        """Send an enhanced chat message with context."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/agents/sessions/{session_id}/chat-enhanced",
                headers=self.headers,
                json={"chat_context": chat_context}
            )
            response.raise_for_status()
            return response.json()


async def test_agent_query():
    """Test the direct agent query functionality."""
    print("Testing Agent Query...")
    
    client = AgentAPIClient()
    agent_id = "d406f37f-5c8f-43ce-8835-a69a9a8a764a"
    
    try:
        response = await client.query_agent(
            agent_id=agent_id,
            query="What is the capital of France?",
            user_id="test_user",
            variables={"context": "geography"}
        )
        
        print(f"✅ Query Response: {json.dumps(response, indent=2)}")
        return True
        
    except Exception as e:
        print(f"❌ Query failed: {e}")
        return False


async def test_session_and_chat():
    """Test session creation and chat functionality."""
    print("\nTesting Session Creation and Chat...")
    
    client = AgentAPIClient()
    agent_id = "d406f37f-5c8f-43ce-8835-a69a9a8a764a"
    
    try:
        # Create session
        session_id = await client.create_session(
            agent_id=agent_id,
            user_id="test_user"
        )
        print(f"✅ Session created: {session_id}")
        
        # Send regular chat message
        chat_response = await client.send_chat_message(
            session_id=session_id,
            message="Hello, can you help me with a task?"
        )
        print(f"✅ Chat Response: {json.dumps(chat_response, indent=2)}")
        
        # Send enhanced chat message
        enhanced_response = await client.send_enhanced_chat_message(
            session_id=session_id,
            chat_context=[
                {"role": "user", "content": "What is machine learning?"},
                {"role": "assistant", "content": "Machine learning is a subset of AI..."},
                {"role": "user", "content": "Can you give me a simple example?"}
            ]
        )
        print(f"✅ Enhanced Chat Response: {json.dumps(enhanced_response, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session/Chat test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Testing Updated Agent Routes")
    print("=" * 50)
    
    # Test agent query
    query_success = await test_agent_query()
    
    # Test session and chat
    session_success = await test_session_and_chat()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Agent Query: {'✅ PASS' if query_success else '❌ FAIL'}")
    print(f"Session/Chat: {'✅ PASS' if session_success else '❌ FAIL'}")
    
    if query_success and session_success:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check the API gateway logs for details.")


if __name__ == "__main__":
    asyncio.run(main())
