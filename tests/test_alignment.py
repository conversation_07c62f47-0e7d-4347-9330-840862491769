#!/usr/bin/env python3
"""
Test script to verify the developer-api-gateway alignment with main api-gateway patterns.

This script tests:
1. Authentication patterns (role_required)
2. Route structure and documentation
3. Response formats
4. Error handling
"""

import asyncio
import httpx
import json
import sys
import os
from typing import Dict, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class AlignmentTestClient:
    """Client for testing the aligned API routes."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        # Mock JWT token for testing (would need real token in production)
        self.headers = {
            "Authorization": "Bearer mock-jwt-token",
            "Content-Type": "application/json"
        }
    
    async def test_openapi_docs(self) -> bool:
        """Test that OpenAPI documentation is accessible and comprehensive."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/docs")
                if response.status_code == 200:
                    print("✅ OpenAPI documentation accessible")
                    return True
                else:
                    print(f"❌ OpenAPI docs failed: {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ OpenAPI docs test failed: {e}")
            return False
    
    async def test_openapi_schema(self) -> bool:
        """Test that OpenAPI schema includes proper documentation."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/openapi.json")
                if response.status_code == 200:
                    schema = response.json()
                    
                    # Check for agent routes
                    paths = schema.get("paths", {})
                    agent_paths = [path for path in paths.keys() if "/agents" in path]
                    
                    if len(agent_paths) > 0:
                        print(f"✅ Found {len(agent_paths)} agent endpoints in schema")
                        
                        # Check for comprehensive documentation
                        sample_path = agent_paths[0]
                        path_info = paths[sample_path]
                        
                        # Check for proper HTTP methods
                        methods = list(path_info.keys())
                        print(f"✅ Endpoint {sample_path} has methods: {methods}")
                        
                        # Check for response examples
                        if methods:
                            method_info = path_info[methods[0]]
                            if "responses" in method_info:
                                print("✅ Response documentation found")
                            else:
                                print("⚠️  No response documentation")
                        
                        return True
                    else:
                        print("❌ No agent endpoints found in schema")
                        return False
                else:
                    print(f"❌ OpenAPI schema failed: {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ OpenAPI schema test failed: {e}")
            return False
    
    async def test_authentication_patterns(self) -> bool:
        """Test that authentication patterns are properly implemented."""
        try:
            # Test without authentication (should fail)
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/agents/sessions",
                    json={"agent_id": "test-agent", "user_id": "test-user"}
                )
                
                if response.status_code in [401, 403]:
                    print("✅ Authentication required (as expected)")
                    return True
                else:
                    print(f"⚠️  Expected 401/403, got {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Authentication test failed: {e}")
            return False
    
    async def test_route_structure(self) -> bool:
        """Test that route structure follows the expected patterns."""
        expected_routes = [
            "/api/v1/agents/sessions",
            "/api/v1/agents/sessions/{session_id}/chat",
            "/api/v1/agents/{agent_id}/sessions/{session_id}/tasks",
            "/api/v1/agents/{agent_id}/run-task",
            "/api/v1/agents/tasks/{task_id}/status",
            "/api/v1/agents/{agent_id}/query",
            "/api/v1/agents/sessions/{session_id}/chat-enhanced",
        ]
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/openapi.json")
                if response.status_code == 200:
                    schema = response.json()
                    paths = schema.get("paths", {})
                    
                    found_routes = []
                    for expected_route in expected_routes:
                        # Convert parameterized routes to check for pattern
                        route_pattern = expected_route.replace("{", "").replace("}", "")
                        
                        for actual_path in paths.keys():
                            if route_pattern.replace("agent_id", "").replace("session_id", "").replace("task_id", "") in actual_path:
                                found_routes.append(expected_route)
                                break
                    
                    print(f"✅ Found {len(found_routes)}/{len(expected_routes)} expected routes")
                    
                    if len(found_routes) >= len(expected_routes) * 0.8:  # 80% threshold
                        return True
                    else:
                        print("❌ Missing too many expected routes")
                        return False
                else:
                    print(f"❌ Route structure test failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Route structure test failed: {e}")
            return False
    
    async def test_error_handling(self) -> bool:
        """Test that error handling follows consistent patterns."""
        try:
            # Test with invalid agent ID (should return proper error format)
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/agents/invalid-agent/query",
                    headers=self.headers,
                    json={"query": "test query"}
                )
                
                # Should get some kind of error response
                if response.status_code >= 400:
                    try:
                        error_data = response.json()
                        if "detail" in error_data:
                            print("✅ Error response has proper format")
                            return True
                        else:
                            print("⚠️  Error response missing 'detail' field")
                            return False
                    except json.JSONDecodeError:
                        print("⚠️  Error response not valid JSON")
                        return False
                else:
                    print(f"⚠️  Expected error response, got {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            return False


async def test_configuration():
    """Test that configuration is properly set up."""
    try:
        from app.core.config import settings
        
        print("🔧 Testing Configuration")
        print("=" * 50)
        
        # Test required settings
        required_settings = [
            'JWT_SECRET_KEY',
            'JWT_ALGORITHM',
            'KAFKA_BOOTSTRAP_SERVERS',
            'AGENT_SERVICE_GRPC_ENABLED'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not hasattr(settings, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            print(f"❌ Missing settings: {missing_settings}")
            return False
        else:
            print("✅ All required settings present")
            return True
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all alignment tests."""
    print("🚀 Testing Developer API Gateway Alignment")
    print("=" * 60)
    
    # Test configuration first
    config_success = await test_configuration()
    
    # Initialize test client
    client = AlignmentTestClient()
    
    # Run tests
    tests = [
        ("OpenAPI Documentation", client.test_openapi_docs),
        ("OpenAPI Schema", client.test_openapi_schema),
        ("Authentication Patterns", client.test_authentication_patterns),
        ("Route Structure", client.test_route_structure),
        ("Error Handling", client.test_error_handling),
    ]
    
    results = {"Configuration": config_success}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}...")
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All alignment tests passed!")
        print("The developer-api-gateway is properly aligned with main api-gateway patterns.")
    elif passed >= total * 0.8:
        print("\n⚠️  Most tests passed, but some issues need attention.")
    else:
        print("\n❌ Multiple alignment issues detected. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
