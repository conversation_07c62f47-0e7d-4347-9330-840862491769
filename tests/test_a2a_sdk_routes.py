#!/usr/bin/env python3
"""
A2A SDK Routes Test Script

This script tests all the A2A SDK routes that are created for each agent
using their agent ID, according to the A2A protocol specification.
"""

import asyncio
import json
import sys
import uuid
from typing import Dict, Any, List

import httpx


class A2ASDKRoutesTester:
    """Test all A2A SDK routes for each agent."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/a2a"
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
        self.discovered_agents = []
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if details:
            print(f"    {details}")
        self.test_results.append((name, success, details))
    
    async def discover_agents(self) -> List[str]:
        """Discover available agents."""
        try:
            response = await self.client.get(f"{self.api_base}/agents")
            if response.status_code == 200:
                data = response.json()
                agents = data.get("agents", [])
                agent_ids = []
                for agent in agents:
                    agent_id = agent.get("id")
                    if agent_id:
                        agent_ids.append(agent_id)
                
                self.discovered_agents = agent_ids
                self.log_test("Agent Discovery", True, f"Found {len(agent_ids)} agents")
                return agent_ids
            else:
                self.log_test("Agent Discovery", False, f"Status: {response.status_code}")
                return []
        except Exception as e:
            self.log_test("Agent Discovery", False, f"Error: {e}")
            return []
    
    async def test_agent_card(self, agent_id: str):
        """Test GET /.well-known/agent.json for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/.well-known/agent.json"
            response = await self.client.get(url)
            success = response.status_code == 200
            
            details = f"Status: {response.status_code}"
            if success:
                data = response.json()
                details += f", Name: {data.get('name', 'Unknown')}"
                details += f", Skills: {len(data.get('skills', []))}"
            
            self.log_test(f"Agent Card ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Agent Card ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_message_send(self, agent_id: str):
        """Test POST /message/send for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/message/send"
            payload = {
                "message": {
                    "role": "user",
                    "parts": [
                        {
                            "kind": "text",
                            "text": "Hello, this is a test message!"
                        }
                    ],
                    "messageId": str(uuid.uuid4())
                }
            }
            
            response = await self.client.post(url, json=payload)
            success = response.status_code in [200, 201, 202]
            
            details = f"Status: {response.status_code}"
            if success and response.headers.get("content-type", "").startswith("application/json"):
                try:
                    data = response.json()
                    if "task" in data or "message" in data:
                        details += ", Response: Valid"
                except:
                    pass
            
            self.log_test(f"Message Send ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Message Send ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_message_stream(self, agent_id: str):
        """Test POST /message/stream for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/message/stream"
            payload = {
                "message": {
                    "role": "user",
                    "parts": [
                        {
                            "kind": "text",
                            "text": "Generate a streaming response"
                        }
                    ],
                    "messageId": str(uuid.uuid4())
                }
            }
            
            response = await self.client.post(
                url, 
                json=payload,
                headers={"Accept": "text/event-stream"}
            )
            success = response.status_code == 200
            content_type = response.headers.get("content-type", "")
            is_sse = "text/event-stream" in content_type
            
            details = f"Status: {response.status_code}, SSE: {is_sse}"
            
            self.log_test(f"Message Stream ({agent_id})", success and is_sse, details)
            return success and is_sse
            
        except Exception as e:
            self.log_test(f"Message Stream ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_tasks_get(self, agent_id: str):
        """Test GET /tasks/get for specific agent."""
        try:
            task_id = str(uuid.uuid4())
            url = f"{self.api_base}/agents/{agent_id}/tasks/get"
            params = {"task_id": task_id}
            
            response = await self.client.get(url, params=params)
            success = response.status_code in [200, 404]  # 404 is acceptable for non-existent task
            
            details = f"Status: {response.status_code}"
            if response.status_code == 404:
                details += " (Task not found - expected)"
            
            self.log_test(f"Tasks Get ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Tasks Get ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_tasks_resubscribe(self, agent_id: str):
        """Test POST /tasks/resubscribe for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/tasks/resubscribe"
            payload = {
                "task_id": str(uuid.uuid4())
            }
            
            response = await self.client.post(
                url, 
                json=payload,
                headers={"Accept": "text/event-stream"}
            )
            success = response.status_code in [200, 404]  # 404 acceptable for non-existent task
            
            details = f"Status: {response.status_code}"
            if response.status_code == 200:
                content_type = response.headers.get("content-type", "")
                is_sse = "text/event-stream" in content_type
                details += f", SSE: {is_sse}"
            
            self.log_test(f"Tasks Resubscribe ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Tasks Resubscribe ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_push_notifications(self, agent_id: str):
        """Test POST/GET /tasks/pushNotifications/* for specific agent."""
        try:
            # Test SET
            set_url = f"{self.api_base}/agents/{agent_id}/tasks/pushNotifications/set"
            set_payload = {
                "url": "https://example.com/webhook",
                "token": "test-token"
            }
            
            set_response = await self.client.post(set_url, json=set_payload)
            set_success = set_response.status_code in [200, 201, 202]
            
            # Test GET
            get_url = f"{self.api_base}/agents/{agent_id}/tasks/pushNotifications/get"
            get_response = await self.client.get(get_url)
            get_success = get_response.status_code in [200, 404]
            
            success = set_success and get_success
            details = f"SET: {set_response.status_code}, GET: {get_response.status_code}"
            
            self.log_test(f"Push Notifications ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Push Notifications ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_tasks_send(self, agent_id: str):
        """Test POST /tasks/send for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/tasks/send"
            payload = {
                "task": {
                    "type": "general",
                    "input": "Test synchronous task",
                    "parameters": {}
                }
            }
            
            response = await self.client.post(url, json=payload)
            success = response.status_code in [200, 201, 202]
            
            details = f"Status: {response.status_code}"
            
            self.log_test(f"Tasks Send ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Tasks Send ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_tasks_send_subscribe(self, agent_id: str):
        """Test POST /tasks/sendSubscribe for specific agent."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/tasks/sendSubscribe"
            payload = {
                "task": {
                    "type": "general",
                    "input": "Test asynchronous task with streaming",
                    "parameters": {}
                }
            }
            
            response = await self.client.post(
                url, 
                json=payload,
                headers={"Accept": "text/event-stream"}
            )
            success = response.status_code == 200
            content_type = response.headers.get("content-type", "")
            is_sse = "text/event-stream" in content_type
            
            details = f"Status: {response.status_code}, SSE: {is_sse}"
            
            self.log_test(f"Tasks Send Subscribe ({agent_id})", success and is_sse, details)
            return success and is_sse
            
        except Exception as e:
            self.log_test(f"Tasks Send Subscribe ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_all_routes_for_agent(self, agent_id: str):
        """Test all A2A SDK routes for a specific agent."""
        print(f"\n🧪 Testing A2A SDK Routes for Agent: {agent_id}")
        print("-" * 60)
        
        # Test all routes for this agent
        await self.test_agent_card(agent_id)
        await self.test_message_send(agent_id)
        await self.test_message_stream(agent_id)
        await self.test_tasks_get(agent_id)
        await self.test_tasks_resubscribe(agent_id)
        await self.test_push_notifications(agent_id)
        await self.test_tasks_send(agent_id)
        await self.test_tasks_send_subscribe(agent_id)
    
    async def run_all_tests(self):
        """Run all A2A SDK route tests."""
        print("🚀 A2A SDK Routes Test")
        print("=" * 60)
        print("Testing the specific A2A SDK routes created for each agent:")
        print("- GET /.well-known/agent.json")
        print("- POST /message/send")
        print("- POST /message/stream")
        print("- GET /tasks/get")
        print("- POST /tasks/resubscribe")
        print("- POST/GET /tasks/pushNotifications/*")
        print("- POST /tasks/send")
        print("- POST /tasks/sendSubscribe")
        print("=" * 60)
        
        # Discover agents
        agent_ids = await self.discover_agents()
        
        if not agent_ids:
            # Use mock agent IDs for testing
            agent_ids = ["test-agent-1", "test-agent-2"]
            print(f"No agents discovered, using mock agent IDs: {agent_ids}")
        
        # Test all routes for each agent
        for agent_id in agent_ids:
            await self.test_all_routes_for_agent(agent_id)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 A2A SDK ROUTES TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        # Group results by agent
        agent_results = {}
        for name, success, details in self.test_results:
            if "(" in name and ")" in name:
                agent_part = name.split("(")[-1].split(")")[0]
                if agent_part not in agent_results:
                    agent_results[agent_part] = []
                agent_results[agent_part].append((name.split("(")[0].strip(), success, details))
            else:
                if "General" not in agent_results:
                    agent_results["General"] = []
                agent_results["General"].append((name, success, details))
        
        for agent, results in agent_results.items():
            print(f"\n{agent}:")
            for name, success, details in results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"  {status} {name}")
        
        print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All A2A SDK routes are working correctly!")
            print("\nYour agents now have the following A2A protocol endpoints:")
            for agent_id in agent_ids:
                print(f"\nAgent: {agent_id}")
                print(f"  - Agent Card: {self.api_base}/agents/{agent_id}/.well-known/agent.json")
                print(f"  - Message Send: {self.api_base}/agents/{agent_id}/message/send")
                print(f"  - Message Stream: {self.api_base}/agents/{agent_id}/message/stream")
                print(f"  - Tasks Get: {self.api_base}/agents/{agent_id}/tasks/get")
                print(f"  - Tasks Resubscribe: {self.api_base}/agents/{agent_id}/tasks/resubscribe")
                print(f"  - Push Notifications: {self.api_base}/agents/{agent_id}/tasks/pushNotifications/*")
                print(f"  - Tasks Send: {self.api_base}/agents/{agent_id}/tasks/send")
                print(f"  - Tasks Send Subscribe: {self.api_base}/agents/{agent_id}/tasks/sendSubscribe")
            return 0
        else:
            print("❌ Some A2A SDK routes need attention.")
            return 1


async def main():
    """Run the A2A SDK routes tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test A2A SDK Routes for Each Agent")
    parser.add_argument(
        "--base-url",
        default="http://localhost:8000",
        help="Base URL of the API server (default: http://localhost:8000)"
    )
    
    args = parser.parse_args()
    
    async with A2ASDKRoutesTester(args.base_url) as tester:
        return await tester.run_all_tests()


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
