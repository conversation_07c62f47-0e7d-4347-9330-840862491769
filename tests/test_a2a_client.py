#!/usr/bin/env python3
"""
Test script for A2A Client functionality.
This script demonstrates how to use the A2A client to interact with agents.
"""

import asyncio
import json
import logging
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

import os

# Configure logging using the new logging system
import sys

from app.services.a2a_agent_manager import A2AAgentManager
from app.services.a2a_client import AutoGenA2AClient

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "app"))

from app.core.logging import get_logger, setup_logging

# Set up logging
setup_logging(default_level="INFO", use_json=False)
logger = get_logger(__name__)


async def test_basic_client():
    """Test basic A2A client functionality."""
    logger.info("🧪 Testing Basic A2A Client")

    async with AutoGenA2AClient() as client:
        # Test agent discovery
        logger.info("1. Testing agent discovery...")
        agents = await client.discover_agents()
        logger.info(f"   Found {len(agents)} agents")

        if not agents:
            logger.warning("   No agents found - make sure the A2A server is running")
            return False

        # Test getting agent card
        agent_id = agents[0].get("id")
        if agent_id:
            logger.info(f"2. Testing agent card retrieval for {agent_id}...")
            agent_card = await client.get_agent_card(agent_id)
            if agent_card:
                logger.info(f"   ✅ Got agent card: {agent_card.name}")
            else:
                logger.error("   ❌ Failed to get agent card")
                return False

        # Test sending a message
        if agent_id:
            logger.info(f"3. Testing message sending to {agent_id}...")
            response = await client.send_message(
                agent_id=agent_id,
                message_text="Hello! This is a test message from the A2A client.",
                metadata={"test": "basic_client"},
            )

            if response:
                logger.info("   ✅ Message sent successfully")
                logger.info(f"   Response: {json.dumps(response, indent=2)}")
            else:
                logger.error("   ❌ Failed to send message")
                return False

        # Test streaming message
        if agent_id:
            logger.info(f"4. Testing streaming message to {agent_id}...")
            stream = await client.send_message_streaming(
                agent_id=agent_id,
                message_text="Please provide a detailed explanation of your capabilities.",
                metadata={"test": "streaming"},
            )

            if stream:
                logger.info("   ✅ Streaming started")
                chunk_count = 0
                async for chunk in stream:
                    chunk_count += 1
                    logger.info(f"   Chunk {chunk_count}: {json.dumps(chunk, indent=2)}")
                    if chunk_count >= 3:  # Limit output for testing
                        break
                logger.info(f"   ✅ Streaming completed ({chunk_count} chunks)")
            else:
                logger.error("   ❌ Failed to start streaming")
                return False

    logger.info("✅ Basic A2A Client test completed successfully!")
    return True


async def test_agent_manager():
    """Test A2A Agent Manager functionality."""
    logger.info("🧪 Testing A2A Agent Manager")

    async with A2AAgentManager() as manager:
        # Test agent discovery
        logger.info("1. Testing agent discovery...")
        agents = await manager.get_agents()
        logger.info(f"   Found {len(agents)} agents")

        if not agents:
            logger.warning("   No agents found - make sure the A2A server is running")
            return False

        # Test sending message to specific agent
        agent_id = agents[0].get("id")
        if agent_id:
            logger.info(f"2. Testing message to specific agent {agent_id}...")
            response = await manager.send_message_to_agent(
                agent_id=agent_id,
                message="What can you help me with?",
                session_id="test-session-123",
                metadata={"test": "agent_manager"},
            )

            if response:
                logger.info("   ✅ Message sent successfully")
            else:
                logger.error("   ❌ Failed to send message")
                return False

        # Test broadcasting to multiple agents
        if len(agents) > 1:
            logger.info("3. Testing broadcast to multiple agents...")
            agent_ids = [agent.get("id") for agent in agents[:2]]  # Test with first 2 agents
            responses = await manager.broadcast_message(
                message="Hello from the A2A client test!",
                agent_ids=agent_ids,
                metadata={"test": "broadcast"},
            )

            successful = sum(1 for r in responses.values() if r is not None)
            logger.info(f"   ✅ Broadcast completed: {successful}/{len(responses)} successful")

        # Test agent selection for task
        logger.info("4. Testing agent selection for task...")
        best_agent = await manager.find_best_agent_for_task(
            task_description="I need help with writing Python code",
            required_capabilities=["coding", "programming"],
        )

        if best_agent:
            logger.info(f"   ✅ Selected agent: {best_agent}")
        else:
            logger.info("   ℹ️ No specific agent selected (using default)")

        # Test task execution with best agent
        logger.info("5. Testing task execution with best agent...")
        task_response = await manager.execute_task_with_best_agent(
            task_description="Explain the concept of recursion in programming",
            metadata={"test": "task_execution"},
        )

        if task_response:
            logger.info("   ✅ Task executed successfully")
        else:
            logger.error("   ❌ Failed to execute task")
            return False

    logger.info("✅ A2A Agent Manager test completed successfully!")
    return True


async def test_error_handling():
    """Test error handling scenarios."""
    logger.info("🧪 Testing Error Handling")

    async with AutoGenA2AClient() as client:
        # Test with non-existent agent
        logger.info("1. Testing with non-existent agent...")
        response = await client.send_message(
            agent_id="non-existent-agent",
            message_text="This should fail",
        )

        if response is None:
            logger.info("   ✅ Correctly handled non-existent agent")
        else:
            logger.warning("   ⚠️ Unexpected response from non-existent agent")

        # Test with invalid agent card URL
        logger.info("2. Testing with invalid agent card...")
        agent_card = await client.get_agent_card("invalid-agent-id")

        if agent_card is None:
            logger.info("   ✅ Correctly handled invalid agent card")
        else:
            logger.warning("   ⚠️ Unexpected agent card retrieved")

    logger.info("✅ Error handling test completed!")
    return True


async def run_all_tests():
    """Run all A2A client tests."""
    logger.info("🚀 Starting A2A Client Tests")
    logger.info("=" * 50)

    tests = [
        ("Basic Client", test_basic_client),
        ("Agent Manager", test_agent_manager),
        ("Error Handling", test_error_handling),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} Test...")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name} Test: PASSED")
            else:
                logger.error(f"❌ {test_name} Test: FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name} Test: ERROR - {e}")
            results[test_name] = False

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Results Summary:")

    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error("💔 Some tests failed!")
        return False


async def main():
    """Main function."""
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()

        if test_type == "basic":
            await test_basic_client()
        elif test_type == "manager":
            await test_agent_manager()
        elif test_type == "errors":
            await test_error_handling()
        else:
            logger.error(f"Unknown test type: {test_type}")
            logger.info("Available test types: basic, manager, errors")
            sys.exit(1)
    else:
        # Run all tests
        success = await run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)
