#!/usr/bin/env python3
"""
A2A Protocol Routes Test Script

This script tests all the A2A protocol routes that have been implemented
according to the official A2A specification.
"""

import asyncio
import json
import sys
import uuid
from datetime import datetime
from typing import Dict, Any

import httpx


class A2AProtocolTester:
    """Test all A2A protocol routes."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/a2a"
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if details:
            print(f"    {details}")
        self.test_results.append((name, success, details))
    
    async def test_well_known_discovery(self):
        """Test well-known agent card discovery."""
        try:
            response = await self.client.get(f"{self.api_base}/.well-known/agent.json")
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success:
                agent_count = len(data.get("agents", []))
                details += f", Agents: {agent_count}"
            
            self.log_test("Well-Known Agent Discovery", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test("Well-Known Agent Discovery", False, f"Error: {e}")
            return None
    
    async def test_agent_list(self):
        """Test agent list endpoint."""
        try:
            response = await self.client.get(f"{self.api_base}/agents")
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success:
                agent_count = len(data.get("agents", []))
                details += f", Agents: {agent_count}"
            
            self.log_test("Agent List Discovery", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test("Agent List Discovery", False, f"Error: {e}")
            return None
    
    async def test_agent_card(self, agent_id: str):
        """Test individual agent card."""
        try:
            response = await self.client.get(f"{self.api_base}/agents/{agent_id}")
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success:
                details += f", Agent: {data.get('agent_card', {}).get('name', 'Unknown')}"
            
            self.log_test(f"Agent Card ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Agent Card ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_well_known_agent_card(self, agent_id: str):
        """Test well-known agent card."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/.well-known/agent.json"
            response = await self.client.get(url)
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success:
                details += f", Name: {data.get('name', 'Unknown')}"
                details += f", Skills: {len(data.get('skills', []))}"
            
            self.log_test(f"Well-Known Agent Card ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Well-Known Agent Card ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_authenticated_extended_card(self, agent_id: str):
        """Test authenticated extended agent card."""
        try:
            url = f"{self.api_base}/agents/{agent_id}/agent/authenticatedExtendedCard"
            response = await self.client.get(url)
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success:
                details += f", Extended: {data.get('supportsAuthenticatedExtendedCard', False)}"
            
            self.log_test(f"Authenticated Extended Card ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Authenticated Extended Card ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_message_send(self, agent_id: str):
        """Test message/send JSON-RPC method."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "message/send",
                "params": {
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "kind": "text",
                                "text": "Hello, this is a test message!"
                            }
                        ],
                        "messageId": str(uuid.uuid4())
                    }
                }
            }
            
            response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success and "result" in data:
                task_state = data.get("result", {}).get("status", {}).get("state", "unknown")
                details += f", Task State: {task_state}"
            
            self.log_test(f"Message Send ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Message Send ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_message_stream(self, agent_id: str):
        """Test message/stream JSON-RPC method."""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "message/stream",
                "params": {
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "kind": "text",
                                "text": "Generate a streaming response"
                            }
                        ],
                        "messageId": str(uuid.uuid4())
                    }
                }
            }
            
            response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "text/event-stream"
                }
            )
            
            success = response.status_code == 200
            content_type = response.headers.get("content-type", "")
            is_sse = "text/event-stream" in content_type
            
            details = f"Status: {response.status_code}"
            if success:
                details += f", SSE: {is_sse}"
                if is_sse:
                    # Read first few lines of SSE stream
                    content = response.text[:200] + "..." if len(response.text) > 200 else response.text
                    details += f", Content: {len(response.text)} chars"
            
            self.log_test(f"Message Stream ({agent_id})", success and is_sse, details)
            return response if success else None
            
        except Exception as e:
            self.log_test(f"Message Stream ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_tasks_get(self, agent_id: str):
        """Test tasks/get JSON-RPC method."""
        try:
            task_id = str(uuid.uuid4())
            payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tasks/get",
                "params": {
                    "id": task_id
                }
            }
            
            response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success and "result" in data:
                task_state = data.get("result", {}).get("status", {}).get("state", "unknown")
                details += f", Task State: {task_state}"
            
            self.log_test(f"Tasks Get ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Tasks Get ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_tasks_cancel(self, agent_id: str):
        """Test tasks/cancel JSON-RPC method."""
        try:
            task_id = str(uuid.uuid4())
            payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tasks/cancel",
                "params": {
                    "id": task_id
                }
            }
            
            response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            success = response.status_code == 200
            data = response.json() if success else {}
            
            details = f"Status: {response.status_code}"
            if success and "result" in data:
                task_state = data.get("result", {}).get("status", {}).get("state", "unknown")
                details += f", Task State: {task_state}"
            
            self.log_test(f"Tasks Cancel ({agent_id})", success, details)
            return data if success else None
            
        except Exception as e:
            self.log_test(f"Tasks Cancel ({agent_id})", False, f"Error: {e}")
            return None
    
    async def test_push_notification_config(self, agent_id: str):
        """Test push notification configuration methods."""
        try:
            # Test set config
            set_payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tasks/pushNotificationConfig/set",
                "params": {
                    "url": "https://example.com/webhook",
                    "token": "test-token",
                    "authentication": {"schemes": ["Bearer"]}
                }
            }
            
            set_response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=set_payload,
                headers={"Content-Type": "application/json"}
            )
            
            set_success = set_response.status_code == 200
            
            # Test get config
            get_payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tasks/pushNotificationConfig/get",
                "params": {
                    "id": str(uuid.uuid4())
                }
            }
            
            get_response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=get_payload,
                headers={"Content-Type": "application/json"}
            )
            
            get_success = get_response.status_code == 200
            
            success = set_success and get_success
            details = f"Set: {set_response.status_code}, Get: {get_response.status_code}"
            
            self.log_test(f"Push Notification Config ({agent_id})", success, details)
            return success
            
        except Exception as e:
            self.log_test(f"Push Notification Config ({agent_id})", False, f"Error: {e}")
            return False
    
    async def test_tasks_resubscribe(self, agent_id: str):
        """Test tasks/resubscribe JSON-RPC method."""
        try:
            task_id = str(uuid.uuid4())
            payload = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tasks/resubscribe",
                "params": {
                    "id": task_id
                }
            }
            
            response = await self.client.post(
                f"{self.api_base}/agents/{agent_id}/a2a",
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "text/event-stream"
                }
            )
            
            success = response.status_code == 200
            content_type = response.headers.get("content-type", "")
            is_sse = "text/event-stream" in content_type
            
            details = f"Status: {response.status_code}, SSE: {is_sse}"
            
            self.log_test(f"Tasks Resubscribe ({agent_id})", success and is_sse, details)
            return response if success else None
            
        except Exception as e:
            self.log_test(f"Tasks Resubscribe ({agent_id})", False, f"Error: {e}")
            return None
    
    async def run_all_tests(self):
        """Run all A2A protocol tests."""
        print("🧪 A2A Protocol Routes Test")
        print("=" * 50)
        
        # Test discovery endpoints
        print("\n1. Testing Agent Discovery Routes...")
        well_known_data = await self.test_well_known_discovery()
        agent_list_data = await self.test_agent_list()
        
        # Get a test agent ID
        test_agent_id = None
        if agent_list_data and agent_list_data.get("agents"):
            # Try to get an agent ID from the agent list
            agents = agent_list_data["agents"]
            if agents:
                test_agent_id = agents[0].get("id")
        
        if not test_agent_id:
            # Use a mock agent ID for testing
            test_agent_id = "test-agent-1"
            print(f"    Using mock agent ID: {test_agent_id}")
        else:
            print(f"    Using real agent ID: {test_agent_id}")
        
        # Test agent-specific discovery
        print(f"\n2. Testing Agent-Specific Discovery ({test_agent_id})...")
        await self.test_agent_card(test_agent_id)
        await self.test_well_known_agent_card(test_agent_id)
        await self.test_authenticated_extended_card(test_agent_id)
        
        # Test JSON-RPC methods
        print(f"\n3. Testing JSON-RPC Methods ({test_agent_id})...")
        await self.test_message_send(test_agent_id)
        await self.test_message_stream(test_agent_id)
        await self.test_tasks_get(test_agent_id)
        await self.test_tasks_cancel(test_agent_id)
        await self.test_push_notification_config(test_agent_id)
        await self.test_tasks_resubscribe(test_agent_id)
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        for name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {name}")
        
        print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All A2A protocol routes are working correctly!")
            return 0
        else:
            print("❌ Some A2A protocol routes need attention.")
            return 1


async def main():
    """Run the A2A protocol tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test A2A Protocol Routes")
    parser.add_argument(
        "--base-url",
        default="http://localhost:8000",
        help="Base URL of the API server (default: http://localhost:8000)"
    )
    
    args = parser.parse_args()
    
    async with A2AProtocolTester(args.base_url) as tester:
        return await tester.run_all_tests()


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
