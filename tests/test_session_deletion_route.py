#!/usr/bin/env python3
"""
Test script for the session deletion route.

This script tests the new DELETE /sessions/{session_id} route to ensure it works correctly.
"""

import asyncio
import json
import uuid
from typing import Dict, Any

import httpx


class SessionDeletionRouteTest:
    """Test class for session deletion route."""

    def __init__(self, base_url: str = "http://localhost:8001"):
        """Initialize the test client."""
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"

    async def test_delete_session_route(self):
        """Test the DELETE /sessions/{session_id} route."""
        print("🧪 Testing Session Deletion Route")
        print("=" * 50)

        async with httpx.AsyncClient() as client:
            # Test 1: Delete session with basic request
            session_id = f"test_session_{uuid.uuid4()}"
            
            print(f"\n📝 Test 1: Basic session deletion")
            print(f"   Session ID: {session_id}")
            
            delete_request = {
                "reason": "test_deletion",
                "force": False
            }
            
            try:
                response = await client.delete(
                    f"{self.api_base}/agents/sessions/{session_id}",
                    json=delete_request,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Status Code: {response.status_code}")
                print(f"   Response: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    print(f"   Session ID: {data.get('session_id')}")
                    print(f"   Deleted At: {data.get('deleted_at')}")
                else:
                    print(f"   ❌ Failed with status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

            # Test 2: Delete session with force=True
            session_id = f"test_session_{uuid.uuid4()}"
            
            print(f"\n📝 Test 2: Force deletion")
            print(f"   Session ID: {session_id}")
            
            delete_request = {
                "reason": "force_test",
                "force": True
            }
            
            try:
                response = await client.delete(
                    f"{self.api_base}/agents/sessions/{session_id}",
                    json=delete_request,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Status Code: {response.status_code}")
                print(f"   Response: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                else:
                    print(f"   ❌ Failed with status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

            # Test 3: Test with minimal request (default values)
            session_id = f"test_session_{uuid.uuid4()}"
            
            print(f"\n📝 Test 3: Minimal request")
            print(f"   Session ID: {session_id}")
            
            delete_request = {}  # Use all defaults
            
            try:
                response = await client.delete(
                    f"{self.api_base}/agents/sessions/{session_id}",
                    json=delete_request,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Status Code: {response.status_code}")
                print(f"   Response: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                else:
                    print(f"   ❌ Failed with status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

    async def test_route_documentation(self):
        """Test that the route appears in the API documentation."""
        print("\n📚 Testing API Documentation")
        print("=" * 50)
        
        async with httpx.AsyncClient() as client:
            try:
                # Get OpenAPI spec
                response = await client.get(f"{self.api_base}/openapi.json")
                
                if response.status_code == 200:
                    openapi_spec = response.json()
                    paths = openapi_spec.get("paths", {})
                    
                    # Check if our route exists
                    session_delete_path = "/agents/sessions/{session_id}"
                    
                    if session_delete_path in paths:
                        path_info = paths[session_delete_path]
                        if "delete" in path_info:
                            print("   ✅ DELETE route found in OpenAPI spec")
                            delete_info = path_info["delete"]
                            print(f"   Summary: {delete_info.get('summary', 'N/A')}")
                            print(f"   Tags: {delete_info.get('tags', [])}")
                            
                            # Check request body schema
                            if "requestBody" in delete_info:
                                print("   ✅ Request body schema found")
                            else:
                                print("   ❌ Request body schema missing")
                                
                            # Check response schema
                            if "responses" in delete_info:
                                responses = delete_info["responses"]
                                if "200" in responses:
                                    print("   ✅ 200 response schema found")
                                else:
                                    print("   ❌ 200 response schema missing")
                        else:
                            print("   ❌ DELETE method not found for session route")
                    else:
                        print("   ❌ Session deletion route not found in OpenAPI spec")
                        print(f"   Available paths: {list(paths.keys())}")
                else:
                    print(f"   ❌ Failed to get OpenAPI spec: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")


async def main():
    """Run the tests."""
    print("🚀 Session Deletion Route Test Suite")
    print("=" * 60)
    
    # Note: This test assumes the API server is running
    # You may need to start the server first or adjust the base URL
    
    tester = SessionDeletionRouteTest()
    
    try:
        await tester.test_delete_session_route()
        await tester.test_route_documentation()
        
        print("\n🎉 Test suite completed!")
        print("\nNote: These tests may fail if:")
        print("- The API server is not running")
        print("- Kafka is not available")
        print("- The session deletion handler is not implemented")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
