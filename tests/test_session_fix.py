#!/usr/bin/env python3
"""
Test script to verify the Kafka session timeout fixes.
"""

import asyncio
import logging
from app.services.kafka_service import kafka_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_agent_session_creation():
    """Test regular agent session creation."""
    try:
        logger.info("Testing agent session creation...")

        # Initialize Kafka service
        await kafka_service.initialize()

        # Test agent session creation
        session_id = await kafka_service.create_agent_session(
            agent_id="test-agent-123", user_id="test-user-456", organization_id="test-org-789"
        )

        logger.info(f"Agent session created successfully: {session_id}")
        return True

    except Exception as e:
        logger.error(f"Agent session creation failed: {e}")
        return False


async def test_orchestration_session_creation():
    """Test orchestration team session creation."""
    try:
        logger.info("Testing orchestration team session creation...")

        # Test orchestration team session creation
        session_id = await kafka_service.create_orchestration_team_session(
            user_id="test-user-456", organization_id="test-org-789"
        )

        logger.info(f"Orchestration team session created successfully: {session_id}")
        return True

    except Exception as e:
        logger.error(f"Orchestration team session creation failed: {e}")
        return False


async def main():
    """Main test function."""
    logger.info("Starting Kafka session timeout fix tests...")

    try:
        # Test both session types
        agent_success = await test_agent_session_creation()
        orch_success = await test_orchestration_session_creation()

        if agent_success and orch_success:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed")

    except Exception as e:
        logger.error(f"Test execution failed: {e}")
    finally:
        # Cleanup
        await kafka_service.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
