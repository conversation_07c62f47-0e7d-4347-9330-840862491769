#!/usr/bin/env python3
"""
Test script to verify A2A protocol implementation.

This script tests the A2A protocol server implementation by:
1. Starting the FastAPI server
2. Testing agent discovery endpoints
3. Testing A2A protocol endpoints
4. Verifying agent cards are properly formatted
"""

import asyncio
import httpx
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"
A2A_BASE_URL = f"{BASE_URL}/api/v1/a2a"


async def test_a2a_info():
    """Test the A2A protocol information endpoint."""
    logger.info("Testing A2A protocol information endpoint...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{A2A_BASE_URL}/")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✓ A2A info endpoint working")
            logger.info(f"  - Protocol: {data.get('name')}")
            logger.info(f"  - Version: {data.get('version')}")
            logger.info(f"  - Registered agents: {len(data.get('registered_agents', []))}")
            return True
        else:
            logger.error(f"✗ A2A info endpoint failed: {response.status_code}")
            return False


async def test_agent_discovery():
    """Test agent discovery endpoints."""
    logger.info("Testing agent discovery endpoints...")
    
    async with httpx.AsyncClient() as client:
        # Test list agents
        response = await client.get(f"{A2A_BASE_URL}/agents")
        
        if response.status_code == 200:
            data = response.json()
            agents = data.get('agents', [])
            logger.info(f"✓ Agent discovery working - found {len(agents)} agents")
            
            # Test individual agent cards
            for agent in agents[:2]:  # Test first 2 agents
                agent_id = agent.get('id')
                if agent_id:
                    card_response = await client.get(f"{A2A_BASE_URL}/agents/{agent_id}")
                    if card_response.status_code == 200:
                        logger.info(f"  ✓ Agent card for {agent_id} accessible")
                    else:
                        logger.error(f"  ✗ Agent card for {agent_id} failed: {card_response.status_code}")
            
            return True
        else:
            logger.error(f"✗ Agent discovery failed: {response.status_code}")
            return False


async def test_a2a_protocol_endpoints():
    """Test A2A protocol endpoints for agents."""
    logger.info("Testing A2A protocol endpoints...")
    
    async with httpx.AsyncClient() as client:
        # First get available agents
        response = await client.get(f"{A2A_BASE_URL}/agents")
        
        if response.status_code != 200:
            logger.error("Cannot get agents for A2A protocol testing")
            return False
        
        agents = response.json().get('agents', [])
        if not agents:
            logger.error("No agents available for A2A protocol testing")
            return False
        
        # Test A2A protocol endpoints for first agent
        agent_id = agents[0].get('id')
        logger.info(f"Testing A2A protocol for agent: {agent_id}")
        
        # Test agent card endpoint (A2A spec format)
        card_url = f"{A2A_BASE_URL}/agents/{agent_id}/.well-known/agent.json"
        card_response = await client.get(card_url)
        
        if card_response.status_code == 200:
            logger.info(f"  ✓ A2A agent card endpoint accessible")
            card_data = card_response.json()
            logger.info(f"    - Agent name: {card_data.get('name')}")
            logger.info(f"    - Agent description: {card_data.get('description')}")
            logger.info(f"    - Skills: {len(card_data.get('skills', []))}")
        else:
            logger.info(f"  ⚠ A2A agent card endpoint not yet available: {card_response.status_code}")
        
        # Test A2A protocol endpoint
        a2a_url = f"{A2A_BASE_URL}/agents/{agent_id}/a2a"
        
        # Test with a simple message/send request
        a2a_request = {
            "jsonrpc": "2.0",
            "id": "test-1",
            "method": "message/send",
            "params": {
                "message": {
                    "role": "user",
                    "parts": [
                        {
                            "type": "text",
                            "text": "Hello, this is a test message"
                        }
                    ],
                    "messageId": "test-message-1"
                }
            }
        }
        
        a2a_response = await client.post(
            a2a_url,
            json=a2a_request,
            headers={"Content-Type": "application/json"}
        )
        
        if a2a_response.status_code == 200:
            logger.info(f"  ✓ A2A protocol endpoint accessible")
            try:
                response_data = a2a_response.json()
                logger.info(f"    - Response ID: {response_data.get('id')}")
                logger.info(f"    - Result type: {type(response_data.get('result'))}")
            except json.JSONDecodeError:
                logger.info(f"    - Response received but not JSON")
        else:
            logger.info(f"  ⚠ A2A protocol endpoint not yet available: {a2a_response.status_code}")
        
        return True


async def main():
    """Run all A2A tests."""
    logger.info("Starting A2A Protocol Implementation Tests")
    logger.info("=" * 50)
    
    tests = [
        ("A2A Info", test_a2a_info),
        ("Agent Discovery", test_agent_discovery),
        ("A2A Protocol Endpoints", test_a2a_protocol_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        
        logger.info("-" * 30)
    
    # Summary
    logger.info("Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("🎉 All A2A tests passed!")
    else:
        logger.info("⚠️  Some tests failed - check implementation")


if __name__ == "__main__":
    asyncio.run(main())
