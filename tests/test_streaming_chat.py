#!/usr/bin/env python3
"""
Test script for the streaming chat functionality.

This script demonstrates how to use the new streaming chat endpoint
that was implemented for agent communication.
"""

import asyncio
import json
import httpx
from typing import AsyncGenerator


async def test_streaming_chat():
    """Test the streaming chat endpoint."""

    # Configuration
    base_url = "http://localhost:8000"
    session_id = "test_session_123"

    # Test data
    request_data = {
        "message": "Can you explain machine learning step by step?",
        "metadata": {"stream_format": "sse", "include_timestamps": True, "context": "educational"},
    }

    print("🚀 Testing Streaming Chat Endpoint")
    print(f"📡 URL: {base_url}/api/v1/agents/sessions/{session_id}/chat/stream")
    print(f"📝 Request: {json.dumps(request_data, indent=2)}")
    print("\n" + "=" * 60)

    try:
        async with httpx.AsyncClient() as client:
            # Send streaming request
            async with client.stream(
                "POST",
                f"{base_url}/api/v1/agents/sessions/{session_id}/chat/stream",
                json=request_data,
                headers={"Accept": "text/plain"},
                timeout=30.0,
            ) as response:

                if response.status_code != 200:
                    print(f"❌ Error: {response.status_code}")
                    print(f"Response: {await response.aread()}")
                    return

                print("✅ Streaming started successfully!")
                print("\n📡 Streaming Response:")
                print("-" * 40)

                chunk_count = 0
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        chunk_count += 1

                        # Parse SSE format
                        if chunk.startswith("data: "):
                            data_part = chunk[6:].strip()

                            if data_part == "[DONE]":
                                print("🏁 Stream completed")
                                break

                            try:
                                chunk_data = json.loads(data_part)
                                print(f"📦 Chunk {chunk_count}:")
                                print(f"   ID: {chunk_data.get('chunk_id', 'N/A')}")
                                print(f"   Type: {chunk_data.get('chunk_type', 'N/A')}")
                                print(f"   Content: {chunk_data.get('content', 'N/A')}")
                                print(f"   Final: {chunk_data.get('is_final', False)}")

                                if chunk_data.get("metadata"):
                                    print(f"   Metadata: {chunk_data['metadata']}")
                                print()

                            except json.JSONDecodeError as e:
                                print(f"⚠️  Failed to parse chunk: {data_part}")

                print(f"✅ Received {chunk_count} chunks total")

    except httpx.ConnectError:
        print("❌ Connection failed. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


async def test_regular_chat_for_comparison():
    """Test the regular (non-streaming) chat endpoint for comparison."""

    base_url = "http://localhost:8000"
    session_id = "test_session_123"

    request_data = {"message": "What is machine learning?"}

    print("\n🔄 Testing Regular Chat Endpoint (for comparison)")
    print(f"📡 URL: {base_url}/api/v1/agents/sessions/{session_id}/chat")
    print(f"📝 Request: {json.dumps(request_data, indent=2)}")
    print("\n" + "=" * 60)

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/api/v1/agents/sessions/{session_id}/chat",
                json=request_data,
                timeout=30.0,
            )

            if response.status_code == 200:
                result = response.json()
                print("✅ Regular chat response:")
                print(json.dumps(result, indent=2))
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")

    except httpx.ConnectError:
        print("❌ Connection failed. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("🧪 Streaming Chat Test Suite")
    print("=" * 60)

    # Run tests
    asyncio.run(test_streaming_chat())
    asyncio.run(test_regular_chat_for_comparison())

    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print("✅ Streaming chat endpoint: /api/v1/agents/sessions/{session_id}/chat/stream")
    print("✅ Uses Server-Sent Events (SSE) format")
    print("✅ Returns real-time chunks as agent generates response")
    print("✅ Includes metadata and chunk information")
    print("✅ Properly handles errors and completion")
