#!/usr/bin/env python3
"""
Test script for conversation integration in agent session creation.

This script tests the new functionality where sessions can be created
with existing conversation context.
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class ConversationIntegrationClient:
    """Client for testing conversation integration with agent sessions."""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url
        self.api_key = api_key or "test-api-key"
        self.headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def create_session_with_conversation(
        self, 
        agent_id: str, 
        conversation_id: str = None
    ) -> str:
        """Create a new agent session with optional conversation context."""
        async with httpx.AsyncClient() as client:
            payload = {"agent_id": agent_id}
            if conversation_id:
                payload["conversation_id"] = conversation_id
                
            response = await client.post(
                f"{self.base_url}/api/v1/agents/sessions",
                headers=self.headers,
                json=payload
            )
            response.raise_for_status()
            data = response.json()
            return data["session_id"]
    
    async def send_chat_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """Send a chat message to an agent session."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/agents/sessions/{session_id}/chat",
                headers=self.headers,
                json={"message": message}
            )
            response.raise_for_status()
            return response.json()


async def test_session_creation_without_conversation():
    """Test creating a session without conversation context (existing functionality)."""
    print("Testing Session Creation Without Conversation...")
    
    client = ConversationIntegrationClient()
    agent_id = "d406f37f-5c8f-43ce-8835-a69a9a8a764a"
    
    try:
        session_id = await client.create_session_with_conversation(agent_id=agent_id)
        print(f"✅ Session created without conversation: {session_id}")
        
        # Test sending a message
        response = await client.send_chat_message(
            session_id=session_id,
            message="Hello, this is a test message without conversation context."
        )
        print(f"✅ Chat response: {response.get('message', 'No message')}")
        return True
        
    except Exception as e:
        print(f"❌ Session creation without conversation failed: {e}")
        return False


async def test_session_creation_with_conversation():
    """Test creating a session with conversation context."""
    print("\nTesting Session Creation With Conversation...")
    
    client = ConversationIntegrationClient()
    agent_id = "d406f37f-5c8f-43ce-8835-a69a9a8a764a"
    # This would be a real conversation ID in a real test
    conversation_id = "conv_123e4567-e89b-12d3-a456-426614174000"
    
    try:
        session_id = await client.create_session_with_conversation(
            agent_id=agent_id,
            conversation_id=conversation_id
        )
        print(f"✅ Session created with conversation: {session_id}")
        
        # Test sending a message (should have conversation context)
        response = await client.send_chat_message(
            session_id=session_id,
            message="Continue our previous conversation about machine learning."
        )
        print(f"✅ Chat response with context: {response.get('message', 'No message')}")
        return True
        
    except Exception as e:
        print(f"❌ Session creation with conversation failed: {e}")
        # This might fail if the conversation service is not available
        # or if the conversation ID doesn't exist
        print("Note: This test may fail if conversation service is not running or conversation ID doesn't exist")
        return False


async def test_schema_validation():
    """Test that the new schema accepts conversation_id parameter."""
    print("\nTesting Schema Validation...")
    
    client = ConversationIntegrationClient()
    agent_id = "d406f37f-5c8f-43ce-8835-a69a9a8a764a"
    
    try:
        # Test with valid conversation_id format
        async with httpx.AsyncClient() as http_client:
            response = await http_client.post(
                f"{client.base_url}/api/v1/agents/sessions",
                headers=client.headers,
                json={
                    "agent_id": agent_id,
                    "conversation_id": "conv_valid-format-123"
                }
            )
            
            if response.status_code in [200, 201]:
                print("✅ Schema accepts conversation_id parameter")
                return True
            else:
                print(f"❌ Schema validation failed: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Schema validation test failed: {e}")
        return False


async def main():
    """Run all conversation integration tests."""
    print("🚀 Testing Conversation Integration in Agent Sessions")
    print("=" * 60)
    
    # Test session creation without conversation (baseline)
    test1_success = await test_session_creation_without_conversation()
    
    # Test session creation with conversation
    test2_success = await test_session_creation_with_conversation()
    
    # Test schema validation
    test3_success = await test_schema_validation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"Session without conversation: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"Session with conversation: {'✅ PASS' if test2_success else '❌ FAIL'}")
    print(f"Schema validation: {'✅ PASS' if test3_success else '❌ FAIL'}")
    
    if test1_success and test3_success:
        print("\n🎉 Core functionality tests passed!")
        if not test2_success:
            print("⚠️  Conversation integration test failed - this may be expected if conversation service is not available")
    else:
        print("\n⚠️  Some core tests failed. Check the implementation and API gateway logs.")


if __name__ == "__main__":
    asyncio.run(main())
