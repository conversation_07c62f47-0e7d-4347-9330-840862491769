"""
Tests for the Kafka service.

This module contains tests for the Kafka service in the API gateway.
"""

import json
import uuid
import pytest
from unittest.mock import patch, AsyncMock, MagicMock, call

from app.services.kafka_service import KafkaService, kafka_service
from app.core.config import settings


@pytest.fixture
def mock_producer():
    """
    Create a mock Kafka producer.
    """
    producer = AsyncMock()
    producer.send_and_wait = AsyncMock()
    producer.start = AsyncMock()
    producer.stop = AsyncMock()
    return producer


@pytest.fixture
def mock_consumer():
    """
    Create a mock Kafka consumer.
    """
    consumer = AsyncMock()
    consumer.start = AsyncMock()
    consumer.stop = AsyncMock()
    return consumer


@pytest.fixture
def mock_kafka_service(mock_producer, mock_consumer):
    """
    Create a mock Kafka service with mocked producer and consumer.
    """
    with patch("app.services.kafka_service.AIOKafkaProducer", return_value=mock_producer), \
         patch("app.services.kafka_service.AIOKafkaConsumer", return_value=mock_consumer):
        
        # Create a new instance of KafkaService
        service = KafkaService()
        
        # Return the service and the mocks
        yield service, mock_producer, mock_consumer


@pytest.mark.asyncio
async def test_initialize(mock_kafka_service):
    """
    Test initializing the Kafka service.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Initialize the service
    await service.initialize()
    
    # Check that the producer and consumer were started
    mock_producer.start.assert_called_once()
    mock_consumer.start.assert_called_once()
    
    # Check that the service is initialized
    assert service._initialized is True


@pytest.mark.asyncio
async def test_cleanup(mock_kafka_service):
    """
    Test cleaning up the Kafka service.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Initialize the service
    await service.initialize()
    
    # Clean up the service
    await service.cleanup()
    
    # Check that the producer and consumer were stopped
    mock_producer.stop.assert_called_once()
    mock_consumer.stop.assert_called_once()
    
    # Check that the service is not initialized
    assert service._initialized is False


@pytest.mark.asyncio
async def test_send_message(mock_kafka_service):
    """
    Test sending a message.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Initialize the service
    await service.initialize()
    
    # Define the message
    topic = "test-topic"
    message = {"key": "value"}
    correlation_id = "test-correlation-id"
    
    # Send the message
    result = await service.send_message(topic, message, correlation_id)
    
    # Check that the producer was called correctly
    mock_producer.send_and_wait.assert_called_once()
    call_args = mock_producer.send_and_wait.call_args[1]
    assert call_args["topic"] == topic
    assert json.loads(call_args["value"].decode("utf-8")) == message
    assert call_args["headers"][0][0] == "correlationId"
    assert call_args["headers"][0][1].decode("utf-8") == correlation_id
    
    # Check that the correlation ID was returned
    assert result == correlation_id


@pytest.mark.asyncio
async def test_wait_for_response(mock_kafka_service):
    """
    Test waiting for a response.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Initialize the service
    await service.initialize()
    
    # Define the correlation ID and response
    correlation_id = "test-correlation-id"
    response = {"key": "value"}
    
    # Set up the future
    future = service.response_handlers[correlation_id] = AsyncMock()
    future.done.return_value = False
    future.result.return_value = response
    
    # Wait for the response
    result = await service.wait_for_response(correlation_id)
    
    # Check that the response was returned
    assert result == response


@pytest.mark.asyncio
async def test_create_agent_session(mock_kafka_service):
    """
    Test creating an agent session.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Mock the send_message and wait_for_response methods
    service.send_message = AsyncMock(return_value="test-correlation-id")
    service.wait_for_response = AsyncMock(return_value={"session_id": "test-session-id"})
    
    # Create an agent session
    result = await service.create_agent_session("test-agent-id", "test-user-id")
    
    # Check that the methods were called correctly
    service.send_message.assert_called_once()
    call_args = service.send_message.call_args[0]
    assert call_args[0] == settings.KAFKA_AGENT_CREATION_TOPIC
    assert call_args[1]["agent_id"] == "test-agent-id"
    assert call_args[1]["user_id"] == "test-user-id"
    
    service.wait_for_response.assert_called_once_with("test-correlation-id", timeout=60)
    
    # Check that the session ID was returned
    assert result == "test-session-id"


@pytest.mark.asyncio
async def test_send_chat_message(mock_kafka_service):
    """
    Test sending a chat message.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Mock the send_message and wait_for_response methods
    service.send_message = AsyncMock(return_value="test-correlation-id")
    service.wait_for_response = AsyncMock(return_value={
        "agent_response": {
            "content": "Test response"
        }
    })
    
    # Send a chat message
    result = await service.send_chat_message("test-session-id", "Hello, agent!")
    
    # Check that the methods were called correctly
    service.send_message.assert_called_once()
    call_args = service.send_message.call_args[0]
    assert call_args[0] == settings.KAFKA_AGENT_CHAT_TOPIC
    assert call_args[1]["session_id"] == "test-session-id"
    assert call_args[1]["chat_context"][0]["content"] == "Hello, agent!"
    
    service.wait_for_response.assert_called_once_with("test-correlation-id")
    
    # Check that the response was returned
    assert result["agent_response"]["content"] == "Test response"


@pytest.mark.asyncio
async def test_execute_agent_task(mock_kafka_service):
    """
    Test executing an agent task.
    """
    service, mock_producer, mock_consumer = mock_kafka_service
    
    # Mock the send_message and wait_for_response methods
    service.send_message = AsyncMock(return_value="test-correlation-id")
    service.wait_for_response = AsyncMock(return_value={
        "task_id": "test-task-id",
        "status": {"state": "completed"},
        "result": {"output": "Test result"}
    })
    
    # Execute an agent task
    task = {
        "type": "question_answering",
        "input": {"question": "What is the capital of France?"}
    }
    result = await service.execute_agent_task("test-agent-id", "test-session-id", task)
    
    # Check that the methods were called correctly
    service.send_message.assert_called_once()
    call_args = service.send_message.call_args[0]
    assert call_args[0] == settings.KAFKA_AGENT_TASK_TOPIC
    assert call_args[1]["agent_id"] == "test-agent-id"
    assert call_args[1]["session_id"] == "test-session-id"
    assert call_args[1]["task"] == task
    
    service.wait_for_response.assert_called_once_with("test-correlation-id", timeout=120)
    
    # Check that the response was returned
    assert result["task_id"] == "test-task-id"
    assert result["status"]["state"] == "completed"
    assert result["result"]["output"] == "Test result"
