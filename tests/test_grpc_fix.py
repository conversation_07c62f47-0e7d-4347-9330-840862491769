#!/usr/bin/env python3
"""
Test script to verify the gRPC connection issue is resolved.

This script tests the agent service initialization with gRPC disabled.
"""

import asyncio
import os
import sys


from app.core.config import settings
from app.services.agent_service import AgentServiceClient

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_agent_service_initialization():
    """Test agent service initialization without gRPC errors."""
    print("🔧 Testing Agent Service Initialization")
    print("=" * 50)

    # Show current configuration
    print(f"gRPC Enabled: {getattr(settings, 'AGENT_SERVICE_GRPC_ENABLED', False)}")
    print(f"Agent Service Host: {settings.AGENT_SERVICE_HOST}")
    print(f"Agent Service Port: {settings.AGENT_SERVICE_PORT}")
    print(f"Agent Service URL: {settings.AGENT_SERVICE_URL}")
    print()

    try:
        # Initialize agent service client
        print("Initializing AgentServiceClient...")
        agent_service = AgentServiceClient()
        print("✅ AgentServiceClient initialized successfully")

        # Test getting all agents
        print("\nTesting get_all_agents()...")
        agents = await agent_service.get_all_agents()
        print(f"✅ Retrieved {len(agents)} agents")

        # Show first few agents
        for i, agent in enumerate(agents[:3]):
            print(f"  {i+1}. {agent.name} ({agent.id}) - {agent.status}")

        # Test getting a specific agent
        print("\nTesting get_agent() with mock agent...")
        agent = await agent_service.get_agent("agent-1")
        if agent:
            print(f"✅ Retrieved agent: {agent.name}")
        else:
            print("❌ Failed to retrieve agent")

        # Clean up
        await agent_service.close()
        print("\n✅ Agent service test completed successfully")
        return True

    except Exception as e:
        print(f"❌ Agent service test failed: {e}")
        return False


async def test_configuration():
    """Test configuration loading."""
    print("\n🔧 Testing Configuration")
    print("=" * 50)

    try:
        # Test that the new configuration option is available
        grpc_enabled = getattr(settings, "AGENT_SERVICE_GRPC_ENABLED", None)

        if grpc_enabled is None:
            print("❌ AGENT_SERVICE_GRPC_ENABLED not found in settings")
            return False

        print(f"✅ AGENT_SERVICE_GRPC_ENABLED: {grpc_enabled}")

        # Test other required settings
        required_settings = [
            "AGENT_SERVICE_HOST",
            "AGENT_SERVICE_PORT",
            "AGENT_SERVICE_URL",
            "KAFKA_BOOTSTRAP_SERVERS",
            "KAFKA_AGENT_CREATION_TOPIC",
            "KAFKA_AGENT_CHAT_TOPIC",
            "KAFKA_AGENT_RESPONSE_TOPIC",
            "KAFKA_AGENT_QUERY_TOPIC",
        ]

        for setting in required_settings:
            value = getattr(settings, setting, None)
            if value is None:
                print(f"❌ {setting} not found in settings")
                return False
            print(f"✅ {setting}: {value}")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Testing gRPC Fix")
    print("=" * 50)

    # Test configuration
    config_success = await test_configuration()

    # Test agent service
    agent_success = await test_agent_service_initialization()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"Agent Service: {'✅ PASS' if agent_success else '❌ FAIL'}")

    if config_success and agent_success:
        print("\n🎉 All tests passed! gRPC issue should be resolved.")
        print("\nThe system will now use mock data instead of trying to connect to gRPC.")
        print("To enable gRPC when the service is available, set AGENT_SERVICE_GRPC_ENABLED=true")
    else:
        print("\n⚠️  Some tests failed. Check the configuration and try again.")


if __name__ == "__main__":
    asyncio.run(main())
