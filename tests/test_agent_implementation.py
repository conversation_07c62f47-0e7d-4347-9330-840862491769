#!/usr/bin/env python3
"""
Test script for the getAgentsByIds implementation and agent details enrichment.
"""

import asyncio
import sys
import os

# Add the parent directory to the Python path to import app modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.agent_grpc_service import AgentServiceClient
from app.utils.agent_utils import (
    fetch_agents_details,
    enrich_conversations_with_agent_details,
    enrich_tasks_with_agent_details,
)


async def test_get_agents_by_ids():
    """Test the getAgentsByIds gRPC function."""
    print("Testing getAgentsByIds gRPC function...")

    agent_service = AgentServiceClient()

    # Test with some example agent IDs (these may not exist, but should not crash)
    test_agent_ids = ["agent_123", "agent_456", "agent_789"]

    try:
        response = await agent_service.getAgentsByIds(test_agent_ids)
        print(f"✅ getAgentsByIds call successful")
        print(f"   Success: {response.success}")
        print(f"   Message: {response.message}")
        print(f"   Agents count: {len(response.agents)}")

        for i, agent in enumerate(response.agents):
            print(f"   Agent {i+1}: ID={agent.id}, Name={agent.name}")

    except Exception as e:
        print(f"❌ getAgentsByIds call failed: {str(e)}")
        return False

    return True


async def test_fetch_agents_details():
    """Test the fetch_agents_details utility function."""
    print("\nTesting fetch_agents_details utility function...")

    test_agent_ids = ["agent_123", "agent_456"]

    try:
        agent_details_map = await fetch_agents_details(test_agent_ids)
        print(f"✅ fetch_agents_details call successful")
        print(f"   Retrieved {len(agent_details_map)} agent details")

        for agent_id, details in agent_details_map.items():
            print(f"   Agent {agent_id}: Name={details.name}, Category={details.category}")

    except Exception as e:
        print(f"❌ fetch_agents_details call failed: {str(e)}")
        return False

    return True


async def test_enrich_conversations():
    """Test the conversation enrichment function."""
    print("\nTesting conversation enrichment...")

    # Mock conversation data
    mock_conversations = [
        {
            "id": "conv_1",
            "userId": "user_123",
            "agentId": "agent_123",
            "chatType": "CHAT_TYPE_AGENT",
            "inputTokens": 100,
            "outputTokens": 200,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "title": "Test Conversation",
            "tasks": [],
        },
        {
            "id": "conv_2",
            "userId": "user_123",
            "agentId": None,  # No agent ID
            "chatType": "CHAT_TYPE_GLOBAL",
            "inputTokens": 50,
            "outputTokens": 75,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "title": "Global Chat",
            "tasks": [],
        },
    ]

    try:
        enriched_conversations = await enrich_conversations_with_agent_details(mock_conversations)
        print(f"✅ Conversation enrichment successful")
        print(f"   Processed {len(enriched_conversations)} conversations")

        for conv in enriched_conversations:
            agent_details = conv.get("agentDetails")
            if agent_details:
                print(f"   Conversation {conv['id']}: Agent details added for {conv['agentId']}")
            else:
                print(
                    f"   Conversation {conv['id']}: No agent details (agentId: {conv.get('agentId')})"
                )

    except Exception as e:
        print(f"❌ Conversation enrichment failed: {str(e)}")
        return False

    return True


async def test_enrich_tasks():
    """Test the task enrichment function."""
    print("\nTesting task enrichment...")

    # Mock task data
    mock_tasks = [
        {
            "id": "task_1",
            "title": "Test Task 1",
            "globalChatConversationId": "conv_global_1",
            "agentConversationId": "conv_agent_1",
            "agentId": "agent_123",
            "taskStatus": "TASK_STATUS_RUNNING",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
        },
        {
            "id": "task_2",
            "title": "Test Task 2",
            "globalChatConversationId": "conv_global_2",
            "agentConversationId": "conv_agent_2",
            "agentId": "agent_456",
            "taskStatus": "TASK_STATUS_COMPLETED",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
        },
    ]

    try:
        enriched_tasks = await enrich_tasks_with_agent_details(mock_tasks)
        print(f"✅ Task enrichment successful")
        print(f"   Processed {len(enriched_tasks)} tasks")

        for task in enriched_tasks:
            agent_details = task.get("agentDetails")
            if agent_details:
                print(f"   Task {task['id']}: Agent details added for {task['agentId']}")
            else:
                print(f"   Task {task['id']}: No agent details for {task['agentId']}")

    except Exception as e:
        print(f"❌ Task enrichment failed: {str(e)}")
        return False

    return True


async def test_enrich_conversations_with_tasks():
    """Test the conversation enrichment function with tasks that have agent details."""
    print("\nTesting conversation enrichment with tasks...")

    # Mock conversation data with tasks
    mock_conversations = [
        {
            "id": "conv_1",
            "userId": "user_123",
            "agentId": "agent_123",
            "chatType": "CHAT_TYPE_AGENT",
            "inputTokens": 100,
            "outputTokens": 200,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z",
            "title": "Test Conversation with Tasks",
            "tasks": [
                {
                    "id": "task_1",
                    "title": "Task 1",
                    "globalChatConversationId": "conv_1",
                    "agentConversationId": "conv_agent_1",
                    "agentId": "agent_456",  # Different agent for task
                    "taskStatus": "TASK_STATUS_RUNNING",
                    "createdAt": "2024-01-01T00:00:00Z",
                    "updatedAt": "2024-01-01T00:00:00Z",
                },
                {
                    "id": "task_2",
                    "title": "Task 2",
                    "globalChatConversationId": "conv_1",
                    "agentConversationId": "conv_agent_2",
                    "agentId": "agent_123",  # Same agent as conversation
                    "taskStatus": "TASK_STATUS_COMPLETED",
                    "createdAt": "2024-01-01T00:00:00Z",
                    "updatedAt": "2024-01-01T00:00:00Z",
                },
            ],
        },
    ]

    try:
        enriched_conversations = await enrich_conversations_with_agent_details(mock_conversations)
        print(f"✅ Conversation with tasks enrichment successful")
        print(f"   Processed {len(enriched_conversations)} conversations")

        for conv in enriched_conversations:
            # Check conversation agent details
            conv_agent_details = conv.get("agentDetails")
            if conv_agent_details:
                print(f"   Conversation {conv['id']}: Agent details added for {conv['agentId']}")
            else:
                print(f"   Conversation {conv['id']}: No agent details (agentId: {conv.get('agentId')})")
            
            # Check tasks agent details
            if conv.get("tasks"):
                print(f"   Conversation {conv['id']} has {len(conv['tasks'])} tasks:")
                for task in conv["tasks"]:
                    task_agent_details = task.get("agentDetails")
                    if task_agent_details:
                        print(f"     Task {task['id']}: Agent details added for {task['agentId']}")
                    else:
                        print(f"     Task {task['id']}: No agent details for {task['agentId']}")

    except Exception as e:
        print(f"❌ Conversation with tasks enrichment failed: {str(e)}")
        return False

    return True


async def main():
    """Run all tests."""
    print("🚀 Starting agent implementation tests...\n")

    tests = [
        test_get_agents_by_ids,
        test_fetch_agents_details,
        test_enrich_conversations,
        test_enrich_tasks,
        test_enrich_conversations_with_tasks,
    ]

    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {str(e)}")
            results.append(False)

    print(f"\n📊 Test Results:")
    print(f"   Passed: {sum(results)}/{len(results)}")
    print(f"   Failed: {len(results) - sum(results)}/{len(results)}")

    if all(results):
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
