apiVersion: v1
kind: ServiceAccount
metadata:
  name: developer-api-ai-sa
  namespace: developer-ruh-ai-dev
  labels:
    name: developer-api-ai-sa
    namespace: developer-ruh-ai-dev
    app: developer-api-ai
    deployment: developer-api-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: developer-api-ai-dp
  namespace: developer-ruh-ai-dev
  labels:
    name: developer-api-ai-dp
    namespace: developer-ruh-ai-dev
    app: developer-api-ai
    serviceaccount: developer-api-ai-sa
    deployment: developer-api-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: developer-api-ai
      deployment: developer-api-ai-dp
  template:
    metadata:
      labels:
        namespace: developer-ruh-ai-dev
        app: developer-api-ai
        deployment: developer-api-ai-dp
    spec:
      serviceAccountName: developer-api-ai-sa      
      containers:
      - name: developer-api-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50056
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: developer-api-ai-svc
  namespace: developer-ruh-ai-dev
spec:
  selector:
    app: developer-api-ai
    deployment: developer-api-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50056
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:developer-api-workflow-hpa
#   namespace:developer-ruh-ai-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:developer-api-workflow-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-service-user-ingress
  namespace: developer-ruh-ai-dev
spec:
  ingressClassName: nginx
  rules:
  - host: developer-api.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: developer-api-ai-svc
            port:
              number: 80


