# Python virtual environments
.venv
venv
env
__pycache__
*.pyc
*.pyo
*.pyd
.Python
.pytest_cache
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.egg-info/
.eggs/
*.egg

# Git
.git
.gitignore
.gitlab-ci.yml

# Environment files
.env
.env.local
.env.*.local

# IDE files
.idea
.vscode
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Kubernetes
k8s-manifest-dev.yml
k8s-manifest-prod.yml

# Local development scripts
run_local.sh
run_tests.sh
cleanup.sh

# Test files
tests/
test_*.py
*_test.py

# Documentation (not needed in container)
doc/
*.md
README.md
TASKLIST.md

# Development tools
.flake8
.mypy.ini
.black
.isort.cfg

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Generated proto files (will be regenerated during build)
proto-definitions/

# Poetry cache
poetry.lock.bak

# Development dependencies cache
.poetry/

# Local configuration overrides
config/local/
.env.development
.env.test

# Build artifacts
build/
dist/
*.tar.gz
*.whl

# Editor backups
*~
*.bak
*.orig

# Coverage reports
.coverage.*
coverage.xml
*.cover

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# PyCharm
.idea/

# VS Code
.vscode/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
