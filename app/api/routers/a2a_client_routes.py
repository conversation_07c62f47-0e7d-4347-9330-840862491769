"""
A2A Client Routes - REST API endpoints for interacting with agents via A2A protocol.
These routes provide a REST interface to the A2A client functionality.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException, Request, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from app.core.logging import get_logger
from app.schemas.responses import DataResponse, ErrorResponse
from app.services.a2a_agent_manager import A2AAgentManager

logger = get_logger(__name__)

# Create router for A2A client functionality
a2a_client_router = APIRouter(
    prefix="/a2a-client",
    tags=["a2a-client"],
    responses={
        401: {"description": "Unauthorized / Invalid credentials"},
        403: {"description": "Forbidden / Insufficient permissions"},
        404: {"description": "Not Found"},
        500: {"description": "Internal Server Error"},
    },
)

# Global A2A agent manager instance
agent_manager: Optional[A2AAgentManager] = None


# Pydantic models for request/response
class SendMessageRequest(BaseModel):
    """
    Request model for sending messages to agents via A2A protocol.

    This model defines the structure for sending messages to specific agents
    through the A2A client interface.
    """

    agent_id: str = Field(
        ...,
        description="Unique identifier of the target agent",
        example="agent_123e4567-e89b-12d3-a456-426614174000",
    )

    message: str = Field(
        ...,
        description="Message content to send to the agent",
        min_length=1,
        max_length=10000,
        example="Please analyze the attached data and provide insights",
    )

    session_id: Optional[str] = Field(
        None,
        description="Optional session ID for conversation context",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )

    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata for the message",
        example={"priority": "high", "timeout": 300, "format": "json", "context": "data_analysis"},
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "agent_id": "agent_123e4567-e89b-12d3-a456-426614174000",
                "message": "Analyze the quarterly sales data and identify trends",
                "session_id": "session_123e4567-e89b-12d3-a456-426614174000",
                "metadata": {
                    "priority": "high",
                    "timeout": 300,
                    "data_source": "sales_db",
                    "output_format": "detailed_report",
                },
            }
        }


class BroadcastMessageRequest(BaseModel):
    """
    Request model for broadcasting messages to multiple agents.

    This model allows sending the same message to multiple agents
    simultaneously through the A2A protocol.
    """

    message: str = Field(
        ...,
        description="Message content to broadcast to all agents",
        min_length=1,
        max_length=10000,
        example="System maintenance will begin in 30 minutes",
    )

    agent_ids: Optional[List[str]] = Field(
        None,
        description="List of specific agent IDs to broadcast to (if None, broadcasts to all)",
        example=["agent_123", "agent_456", "agent_789"],
    )

    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata for the broadcast",
        example={
            "broadcast_type": "system_notification",
            "priority": "urgent",
            "requires_acknowledgment": True,
        },
    )


class TaskRequest(BaseModel):
    """
    Request model for task execution via A2A protocol.

    This model defines the structure for requesting task execution
    from agents through the A2A client interface.
    """

    task_description: str = Field(
        ...,
        description="Detailed description of the task to be executed",
        min_length=1,
        max_length=5000,
        example="Analyze customer feedback data and generate sentiment analysis report",
    )

    required_capabilities: Optional[List[str]] = Field(
        None,
        description="List of capabilities required for the task",
        example=["data_analysis", "natural_language_processing", "report_generation"],
    )

    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional task metadata and parameters",
        example={
            "deadline": "2023-12-01T18:00:00Z",
            "data_source": "customer_feedback_db",
            "output_format": "pdf_report",
            "include_visualizations": True,
        },
    )


class AgentSelectionRequest(BaseModel):
    """
    Request model for intelligent agent selection.

    This model is used to request the best agent for a specific task
    based on capabilities and requirements.
    """

    task_description: str = Field(
        ...,
        description="Description of the task for agent selection",
        min_length=1,
        max_length=5000,
        example="Need to process financial data and generate compliance reports",
    )

    required_capabilities: Optional[List[str]] = Field(
        None,
        description="Required capabilities for the task",
        example=["financial_analysis", "compliance_reporting", "data_processing"],
    )


# Startup and shutdown events
@a2a_client_router.on_event("startup")
async def initialize_agent_manager():
    """Initialize the A2A agent manager."""
    global agent_manager
    try:
        agent_manager = A2AAgentManager()
        await agent_manager.initialize()
        logger.info("A2A Agent Manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize A2A Agent Manager: {e}")


@a2a_client_router.on_event("shutdown")
async def cleanup_agent_manager():
    """Clean up the A2A agent manager."""
    global agent_manager
    if agent_manager:
        await agent_manager.close()
        agent_manager = None
        logger.info("A2A Agent Manager cleaned up")


# Helper function to ensure agent manager is available
async def get_agent_manager() -> A2AAgentManager:
    """Get the agent manager instance, initializing if necessary."""
    global agent_manager
    if not agent_manager:
        agent_manager = A2AAgentManager()
        await agent_manager.initialize()
    return agent_manager


# API Endpoints
@a2a_client_router.get(
    "/",
    summary="A2A Client Information",
    description="Returns information about the A2A client capabilities",
    response_model=dict,
)
async def a2a_client_info():
    """Get information about the A2A client implementation."""
    return {
        "name": "A2A Client",
        "version": "1.0.0",
        "description": "Client for interacting with A2A-compliant agents",
        "capabilities": [
            "Agent discovery",
            "Message sending",
            "Streaming responses",
            "Task management",
            "Agent selection",
            "Broadcasting",
        ],
        "endpoints": {
            "discovery": {
                "list_agents": "/api/v1/a2a-client/agents",
                "get_agent": "/api/v1/a2a-client/agents/{agent_id}",
                "refresh_agents": "/api/v1/a2a-client/agents/refresh",
            },
            "messaging": {
                "send_message": "/api/v1/a2a-client/message",
                "stream_message": "/api/v1/a2a-client/message/stream",
                "broadcast": "/api/v1/a2a-client/broadcast",
            },
            "task_management": {
                "execute_task": "/api/v1/a2a-client/task/execute",
                "select_agent": "/api/v1/a2a-client/task/select-agent",
            },
        },
    }


@a2a_client_router.get(
    "/agents",
    summary="List Available Agents",
    description="Get list of all available agents",
    response_model=List[Dict[str, Any]],
)
async def list_agents(force_refresh: bool = False):
    """List all available agents."""
    try:
        manager = await get_agent_manager()
        agents = await manager.get_agents(force_refresh=force_refresh)
        return agents
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list agents: {str(e)}",
        )


@a2a_client_router.post(
    "/agents/refresh",
    summary="Refresh Agent List",
    description="Force refresh the list of available agents",
    response_model=List[Dict[str, Any]],
)
async def refresh_agents():
    """Force refresh the list of available agents."""
    try:
        manager = await get_agent_manager()
        agents = await manager.refresh_agents()
        return agents
    except Exception as e:
        logger.error(f"Error refreshing agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refresh agents: {str(e)}",
        )


@a2a_client_router.get(
    "/agents/{agent_id}",
    summary="Get Agent Information",
    description="Get detailed information about a specific agent",
    response_model=Dict[str, Any],
)
async def get_agent(agent_id: str):
    """Get information about a specific agent."""
    try:
        manager = await get_agent_manager()
        agent = await manager.get_agent(agent_id)

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"Agent {agent_id} not found"
            )

        # Also get the agent card for more details
        agent_card = await manager.get_agent_card(agent_id)

        result = {
            "agent_info": agent,
            "agent_card": agent_card.model_dump() if agent_card else None,
        }

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent: {str(e)}",
        )


@a2a_client_router.post(
    "/message",
    summary="Send Message to Agent",
    description="Send a message to a specific agent",
    response_model=Dict[str, Any],
)
async def send_message(request: SendMessageRequest):
    """Send a message to a specific agent."""
    try:
        manager = await get_agent_manager()
        response = await manager.send_message_to_agent(
            agent_id=request.agent_id,
            message=request.message,
            session_id=request.session_id,
            metadata=request.metadata,
        )

        if not response:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get response from agent",
            )

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send message: {str(e)}",
        )


@a2a_client_router.post(
    "/message/stream",
    summary="Send Streaming Message to Agent",
    description="Send a message to an agent with streaming response",
)
async def stream_message(request: SendMessageRequest):
    """Send a message to an agent with streaming response."""
    try:
        manager = await get_agent_manager()
        stream = await manager.send_streaming_message_to_agent(
            agent_id=request.agent_id,
            message=request.message,
            session_id=request.session_id,
            metadata=request.metadata,
        )

        if not stream:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start streaming from agent",
            )

        async def generate_response():
            try:
                async for chunk in stream:
                    yield f"data: {json.dumps(chunk)}\n\n"
            except Exception as e:
                logger.error(f"Error in streaming: {e}")
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
            finally:
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error streaming message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stream message: {str(e)}",
        )


@a2a_client_router.post(
    "/broadcast",
    summary="Broadcast Message to Multiple Agents",
    description="Send a message to multiple agents simultaneously",
    response_model=Dict[str, Any],
)
async def broadcast_message(request: BroadcastMessageRequest):
    """Broadcast a message to multiple agents."""
    try:
        manager = await get_agent_manager()
        responses = await manager.broadcast_message(
            message=request.message, agent_ids=request.agent_ids, metadata=request.metadata
        )

        return {
            "message": request.message,
            "agents_contacted": len(responses),
            "responses": responses,
            "successful_responses": sum(1 for r in responses.values() if r is not None),
            "failed_responses": sum(1 for r in responses.values() if r is None),
        }
    except Exception as e:
        logger.error(f"Error broadcasting message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to broadcast message: {str(e)}",
        )


@a2a_client_router.post(
    "/task/select-agent",
    summary="Select Best Agent for Task",
    description="Find the best agent for a given task",
    response_model=Dict[str, Any],
)
async def select_agent_for_task(request: AgentSelectionRequest):
    """Select the best agent for a given task."""
    try:
        manager = await get_agent_manager()
        agent_id = await manager.find_best_agent_for_task(
            task_description=request.task_description,
            required_capabilities=request.required_capabilities,
        )

        if not agent_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No suitable agent found for the task"
            )

        # Get agent details
        agent = await manager.get_agent(agent_id)
        agent_card = await manager.get_agent_card(agent_id)

        return {
            "selected_agent_id": agent_id,
            "agent_info": agent,
            "agent_card": agent_card.model_dump() if agent_card else None,
            "task_description": request.task_description,
            "required_capabilities": request.required_capabilities,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error selecting agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to select agent: {str(e)}",
        )


@a2a_client_router.post(
    "/task/execute",
    summary="Execute Task with Best Agent",
    description="Automatically select the best agent and execute a task",
    response_model=Dict[str, Any],
)
async def execute_task(request: TaskRequest):
    """Execute a task by automatically selecting the best agent."""
    try:
        manager = await get_agent_manager()
        response = await manager.execute_task_with_best_agent(
            task_description=request.task_description,
            required_capabilities=request.required_capabilities,
            metadata=request.metadata,
        )

        if not response:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to execute task"
            )

        return {
            "task_description": request.task_description,
            "execution_response": response,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute task: {str(e)}",
        )
