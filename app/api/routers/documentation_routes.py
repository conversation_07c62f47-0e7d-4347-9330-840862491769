from fastapi import APIRouter, Depends, HTTPException, status
from app.core.auth_guard import role_required
from app.schemas.documentation import (
    DocumentationCategoryEnum,
    DocumentationItem,
    DocumentationListResponse,
    DocumentationContentResponse,
)
from typing import List, Optional

docs_router = APIRouter(prefix="/docs", tags=["documentation"])


@docs_router.get(
    "/list",
    response_model=DocumentationListResponse,
    summary="List documentation items",
    description="Returns a list of available documentation items",
)
async def list_documentation(category: Optional[DocumentationCategoryEnum] = None):
    """
    List available documentation items, optionally filtered by category.

    Args:
        category: Optional category to filter by

    Returns:
        List of documentation items
    """
    # In a real implementation, this would fetch data from a database or service
    # For now, we'll return hardcoded data
    docs = [
        DocumentationItem(
            id="getting-started",
            title="Getting Started",
            category=DocumentationCategoryEnum.GUIDES,
            summary="Learn how to get started with our API",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="authentication",
            title="Authentication",
            category=DocumentationCategoryEnum.GUIDES,
            summary="Learn how to authenticate with our API",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="rate-limiting",
            title="Rate Limiting",
            category=DocumentationCategoryEnum.GUIDES,
            summary="Learn about our rate limiting policies",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="api-reference",
            title="API Reference",
            category=DocumentationCategoryEnum.REFERENCE,
            summary="Complete API reference documentation",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="sdk-python",
            title="Python SDK",
            category=DocumentationCategoryEnum.SDKS,
            summary="Documentation for the Python SDK",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="sdk-javascript",
            title="JavaScript SDK",
            category=DocumentationCategoryEnum.SDKS,
            summary="Documentation for the JavaScript SDK",
            updated_at="2023-06-01",
        ),
        DocumentationItem(
            id="faq",
            title="FAQ",
            category=DocumentationCategoryEnum.SUPPORT,
            summary="Frequently asked questions",
            updated_at="2023-06-01",
        ),
    ]

    # Filter by category if specified
    if category:
        docs = [doc for doc in docs if doc.category == category]

    return DocumentationListResponse(
        success=True, message="Documentation items retrieved successfully", items=docs
    )


@docs_router.get(
    "/{doc_id}",
    response_model=DocumentationContentResponse,
    summary="Get documentation content",
    description="Returns the content of a specific documentation item",
)
async def get_documentation_content(doc_id: str):
    """
    Get the content of a specific documentation item.

    Args:
        doc_id: ID of the documentation item

    Returns:
        Content of the documentation item
    """
    # In a real implementation, this would fetch data from a database or service
    # For now, we'll return hardcoded data

    # Define some sample content for a few documentation items
    content_map = {
        "getting-started": {
            "title": "Getting Started",
            "content": """
# Getting Started with Our API

Welcome to our API documentation! This guide will help you get started with using our API.

## Prerequisites

Before you begin, you'll need:

- An account on our platform
- An API key (you can generate one in your account settings)

## Making Your First Request

Here's a simple example of how to make a request to our API:

```python
import requests

api_key = "your_api_key_here"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

response = requests.get("https://api.example.com/v1/users", headers=headers)
data = response.json()
print(data)
```

## Next Steps

- Check out our [Authentication Guide](/docs/authentication)
- Learn about [Rate Limiting](/docs/rate-limiting)
- Explore the [API Reference](/docs/api-reference)
            """,
            "category": DocumentationCategoryEnum.GUIDES,
            "updated_at": "2023-06-01",
        },
        "authentication": {
            "title": "Authentication",
            "content": """
# Authentication

Our API uses API keys for authentication. You can generate an API key in your account settings.

## API Key Authentication

To authenticate your requests, include your API key in the `Authorization` header:

```
Authorization: Bearer your_api_key_here
```

## Example

```python
import requests

api_key = "your_api_key_here"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

response = requests.get("https://api.example.com/v1/users", headers=headers)
```

## Security Best Practices

- Keep your API key secure and don't share it
- Rotate your API keys regularly
- Use different API keys for different environments (development, production)
            """,
            "category": DocumentationCategoryEnum.GUIDES,
            "updated_at": "2023-06-01",
        },
    }

    # Check if the requested documentation exists
    if doc_id not in content_map:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"Documentation item '{doc_id}' not found"
        )

    # Return the documentation content
    doc = content_map[doc_id]
    return DocumentationContentResponse(
        success=True,
        message=f"Documentation content for '{doc_id}' retrieved successfully",
        id=doc_id,
        title=doc["title"],
        content=doc["content"],
        category=doc["category"],
        updated_at=doc["updated_at"],
    )
