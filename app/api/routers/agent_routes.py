"""
Agent routes for the Developer API Gateway.

This module provides comprehensive routes for agent management including:
- Creating and managing agent sessions
- Sending chat messages to agents
- Executing tasks with agents
- Querying agents directly
- Monitoring task status

All routes follow RESTful conventions and include comprehensive documentation,
input validation, and error handling.
"""

import json
import queue
import uuid
from logging import Logger

from fastapi import APIRouter, Depends, HTTPException, Path, status
from fastapi.responses import StreamingResponse

from app.core.auth_guard import role_required
from app.core.logging import get_logger
from app.helper.sse_manager import sse_manager
from app.schemas.agent import (
    AgentQueryRequest,
    AgentQueryResponse,
    AgentTaskStatus,
    ChatMessageRequest,
    ChatMessageResponse,
    CreateSessionRequest,
    CreateSessionResponse,
    DeleteSessionRequest,
    DeleteSessionResponse,
    EnhancedChatRequest,
    EnhancedChatResponse,
    ExecuteTaskRequest,
    ExecuteTaskResponse,
    HumanInputResponse,
    MessageProcessingStatus,
    OrchestrationTeamChatRequest,
    OrchestrationTeamChatResponse,
    OrchestrationTeamSessionRequest,
    OrchestrationTeamSessionResponse,
    QuickChatMessageResponse,
    RunAgentTaskRequest,
    RunAgentTaskResponse,
)
from app.schemas.communication import MessageList, MessageResponse
from app.services.agent_service import AgentServiceClient
from app.services.background_tasks import background_task_manager
from app.services.communication_service import CommunicationServiceClient
from app.services.kafka_service import kafka_service
from app.shared.constants import MessageStatus, MessageType, SenderType
from app.utils.redis.redis_service import RedisService

# Get logger
logger: Logger = get_logger(__name__)


def get_request_id() -> str:
    """Generate a unique request ID for tracking."""
    return f"req_{uuid.uuid4().hex[:16]}"


async def get_session_data(session_id: str) -> dict:
    """
    Get session data from Redis.

    Args:
        session_id: The session ID

    Returns:
        Session data dictionary or None if not found
    """
    session_key = f"session:{session_id}"
    session_data = redis_service.get_data_from_redis(session_key, "data")
    return session_data


async def store_chat_messages(
    conversation_id: str, user_request: dict, agent_response: str, user_id: str
) -> None:
    """
    Store user message and agent response in the communication service.

    Args:
        conversation_id: The conversation ID
        user_request: The complete user request object to store in data field
        agent_response: The agent's response content
        user_id: The user ID
    """
    try:
        # Store user message with complete request data
        if user_request:
            await communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_USER,
                data=user_request,  # Store the entire request object
                userId=user_id,
                status=MessageStatus.MESSAGE_STATUS_COMPLETED,
                type=MessageType.MESSAGE_TYPE_USER_MESSAGE,
            )
            logger.info(
                "User message stored successfully",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "request_keys": (
                        list(user_request.keys()) if isinstance(user_request, dict) else "non-dict"
                    ),
                },
            )

        # Store agent response
        if agent_response:
            await communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_ASSISTANT,
                data={"message": agent_response},
                userId=user_id,
                status=MessageStatus.MESSAGE_STATUS_COMPLETED,
                type=MessageType.MESSAGE_TYPE_CHAT,
            )
            logger.info(
                "Agent response stored successfully",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "response_length": len(agent_response),
                },
            )
    except Exception as e:
        logger.error(
            f"Error storing chat messages: {str(e)}",
            extra={
                "conversation_id": conversation_id,
                "user_id": user_id,
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )


# Create router with enhanced Swagger documentation
router = APIRouter(
    prefix="/agents",
    tags=["Agent Management"],
    responses={
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {"example": {"detail": "Invalid authentication credentials"}}
            },
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {
                "application/json": {
                    "example": {"detail": "Insufficient permissions for this operation"}
                }
            },
        },
        404: {
            "description": "Not Found - Resource not found",
            "content": {"application/json": {"example": {"detail": "Resource not found"}}},
        },
        422: {
            "description": "Unprocessable Entity - Invalid request data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "field_name"],
                                "msg": "field required",
                                "type": "value_error.missing",
                            }
                        ]
                    }
                }
            },
        },
        429: {
            "description": "Too Many Requests - Rate limit exceeded",
            "content": {
                "application/json": {
                    "example": {"detail": "Rate limit exceeded. Please try again later."}
                }
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {
                "application/json": {"example": {"detail": "Internal server error occurred"}}
            },
        },
    },
)

# Create agent service client
agent_service = AgentServiceClient()

# Create communication service client
communication_service = CommunicationServiceClient()

# Create Redis service for session management
redis_service = RedisService()


# =============================================================================
# ROUTE DEFINITIONS
# =============================================================================


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest,
    current_user: dict = Depends(role_required(["user"])),
) -> CreateSessionResponse:
    """
    Create a new agent session for conversation and task execution.

    This endpoint creates a new session with the specified agent, which serves as a
    conversation context for subsequent chat messages and task executions. Each session
    maintains its own conversation history and state.

    ## Request Body
    - **conversation_id**: conversation ID to initialize session with existing conversation history
    - **force_new_session**: If True, always create a new session even if a cached session exists. If False (default), return existing cached session if available.

    ## Response
    Returns a session identifier that should be used for subsequent interactions.

    ## Example
    ```
    POST /api/v1/agents/sessions
    {
        "conversation_id": "conv_123e4567-e89b-12d3-a456-************",
        "force_new_session": false
    }
    ```

    ## Session Caching Behavior
    - **force_new_session=false** (default): Returns existing cached session if available for the conversation
    - **force_new_session=true**: Always creates a new session, ignoring any cached session

    ## Conversation Integration
    When a conversation_id is provided:
    - The system fetches the existing conversation data and messages
    - All previous messages are loaded as conversation context for the agent
    - The agent_id from the conversation is used if it differs from the request
    - The session is initialized with the full conversation history

    ## Errors
    - 400: Invalid request (missing agent_id, invalid format)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 500: Server error
    """

    try:

        # Use the authenticated user's ID if not specified
        user_id = current_user.get("user_id")
        organization_id = current_user.get("organization_id")
        email = current_user.get("email")
        name = current_user.get("name")

        conversation_id = request.conversation_id

        key = f"conversation:{conversation_id}"

        # Check for existing session only if force_new_session is False
        if not request.force_new_session:
            conversation_data = redis_service.get_data_from_redis(
                key,
                "conversation",
            )  # 1 hour

            if conversation_data and conversation_data.get("session_id"):
                session_id = conversation_data.get("session_id")
                logger.info(
                    "Returning existing cached session",
                    extra={
                        "user_id": user_id,
                        "conversation_id": conversation_id,
                        "session_id": session_id,
                        "operation": "create_session",
                    },
                )
                return CreateSessionResponse(session_id=session_id)

        # Log the request
        logger.info(
            "Creating agent session",
            extra={
                "user_id": user_id,
                "conversation_id": conversation_id,
                "force_new_session": request.force_new_session,
                "operation": "create_session",
            },
        )

        # Initialize conversation context
        conversation_messages = []

        # Get conversation messages
        messages_response_raw = await communication_service.list_messages(
            conversation_id, user_id, page=1, limit=10
        )

        messages_response = MessageList(
            data=[MessageResponse(**msg) for msg in messages_response_raw["data"]],
            metadata=messages_response_raw["metadata"],
        )

        if messages_response.data:
            messages_data = messages_response.data

            # Convert messages to chat context format
            for msg in messages_data:
                sender_type = msg.senderType
                content = None

                # Extract content from data field (now a dictionary)
                if msg.data and isinstance(msg.data, dict):
                    content = msg.data.get("message")

                # If data is null but workflowResponse exists, extract from workflow response
                elif msg.data is None and hasattr(msg, "workflowResponse") and msg.workflowResponse:
                    # Process only the last workflow response item to extract content
                    workflow_contents = []

                    # Get only the last item from workflowResponse
                    if msg.workflowResponse and len(msg.workflowResponse) > 0:
                        last_workflow_item = msg.workflowResponse[-1]

                        if last_workflow_item and "raw_result" in last_workflow_item:
                            raw_result = last_workflow_item["raw_result"]

                            # Handle different raw_result data structures
                            if isinstance(raw_result, dict):
                                if "data" in raw_result:
                                    workflow_contents.append(str(raw_result["data"]))
                            elif isinstance(raw_result, list) and len(raw_result) > 0:
                                # Process all items in the raw_result list
                                for item in raw_result:
                                    if isinstance(item, dict) and "data" in item:
                                        workflow_contents.append(str(item["data"]))
                            elif isinstance(raw_result, str):
                                workflow_contents.append(raw_result)

                    # Join all workflow contents with a separator
                    if workflow_contents:
                        content = "\n\n".join(workflow_contents)

                if content:  # Only include messages with content
                    if len(content) > 5000:
                        content = content[:5000] + "... [content truncated]"

                    role = "user" if sender_type == "SENDER_TYPE_USER" else "assistant"
                    conversation_messages.append({"role": role, "content": content})

            logger.info(
                "Loaded conversation messages for session creation",
                extra={
                    "conversation_id": conversation_id,
                    "message_count": len(conversation_messages),
                    "operation": "create_session",
                },
            )

        session_type = None

        conversation_messages.append(
            {"role": "user", "content": f"My name is {name} and email {email}"}
        )
        conversation_messages.reverse()

        # Create session via Kafka with conversation context
        if request.use_orchestration_team:
            # Create orchestration team session
            session_id = await kafka_service.create_orchestration_team_session(
                user_id=user_id,
                organization_id=organization_id,
                conversation_messages=conversation_messages,
                variables={},
            )
            session_type = "orchestration_team"
        else:
            # Create regular agent session
            session_id = await kafka_service.create_agent_session(
                request.agent_id,
                user_id,
                organization_id=organization_id,
                use_knowledge=request.use_knowledge,
                conversation_messages=conversation_messages,
            )

        # Store session-conversation mapping in Redis for quick lookup
        session_key = f"session:{session_id}"
        session_data = {
            "conversation_id": conversation_id,
            "user_id": user_id,
            "agent_id": request.agent_id,
            "session_type": session_type,
            "organization_id": organization_id,
        }

        redis_service.set_data_to_redis(session_key, "data", session_data)

        # Log the session creation
        logger.info(
            "Agent session created successfully",
            extra={
                "user_id": user_id,
                "agent_id": request.agent_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "operation": "create_session",
            },
        )

        conversation_data = {"session_id": session_id, "session_type": "chat"}

        redis_service.set_data_to_redis(key, "conversation", conversation_data, 3600)  # 1 hour

        return CreateSessionResponse(session_id=session_id)
    except Exception as e:
        # Log the error
        logger.error(
            f"Error creating agent session: {str(e)}",
            extra={
                "user_id": user_id if "user_id" in locals() else None,
                "operation": "create_session",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.delete("/sessions/{session_id}", response_model=DeleteSessionResponse)
async def delete_session(
    request: DeleteSessionRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session to delete",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Delete an agent session and clean up associated resources.

    This endpoint allows you to delete an existing agent session, which will
    terminate the conversation context and clean up any associated resources.
    Once deleted, the session cannot be used for further interactions.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to delete

    ## Request Body
    - **reason**: Optional reason for deletion (e.g., "chat_complete", "user_request")
    - **force**: Whether to force delete even if the session doesn't exist

    ## Response
    Returns confirmation of deletion with session details and timestamp.

    ## Example
    ```
    DELETE /api/v1/agents/sessions/session_123
    {
        "reason": "chat_complete",
        "force": false
    }
    ```

    ## Errors
    - 400: Invalid request (invalid sessionn ID format)
    - 401: Unauthorised (invalid or missingng authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found (unless force=true)
    - 500: Server error
    """
    # Generate a request ID
    request_id = get_request_id()

    try:
        # Use the authenticated user's ID if available
        user_id = current_user.get("user_id", current_user.get("id"))

        # Log the request
        logger.info(
            "Deleting agent session",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "reason": request.reason,
                "force": request.force,
                "operation": "delete_session",
            },
        )

        # Delete session via Kafka
        response = await kafka_service.delete_agent_session(
            session_id=session_id,
            user_id=user_id,
            reason=request.reason,
            force=request.force,
        )

        # Extract response data
        success = response.get("success", False)
        message = response.get("message", "Session deletion processed")
        deleted_at = response.get("deleted_at")

        # Log the session deletion
        logger.info(
            "Agent session deletion processed",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "success": success,
                "deleted_at": deleted_at,
                "operation": "delete_session",
            },
        )

        return DeleteSessionResponse(
            success=success,
            session_id=session_id,
            message=message,
            deleted_at=deleted_at,
        )

    except Exception as e:
        # Log the error
        logger.error(
            f"Error deleting agent session: {str(e)}",
            extra={
                "request_id": request_id,
                "user_id": user_id if "user_id" in locals() else None,
                "session_id": session_id,
                "operation": "delete_session",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


@router.post("/sessions/{session_id}/chat", response_model=ChatMessageResponse)
async def send_chat_message(
    request: ChatMessageRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent within an existing session.

    This endpoint allows you to send a message to an agent within the context of an
    existing session. The agent will process the message and return a response. If the
    agent needs to perform background tasks, a task ID will be included in the response.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)
    - **attachments**: Optional list of file attachments (images, documents, etc.)

    ## Response
    Returns the agent's response message and optionally a task ID for background processing.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat
    {
        "message": "Can you analyze the sales data from last quarter?",
        "attachments": [
            {
                "file_name": "sales_data.csv",
                "file_type": "text/csv",
                "file_size": 1024,
                "file_data": "base64_encoded_content...",
                "metadata": {"description": "Q4 sales data"}
            }
        ]
    }
    ```

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        # Check if this is an orchestration team session
        orch_session_key = f"session:{session_id}"
        orch_session_data = redis_service.get_data_from_redis(orch_session_key, "data")

        if orch_session_data and orch_session_data.get("session_type") == "orchestration_team":
            # Send message to orchestration team
            response = await kafka_service.send_orchestration_team_message(
                session_id=session_id,
                message=request.message,
                attachments=request.attachments,
                mode=request.mode,
                tools=request.tools,
                resource=request.resource.value,
            )
        else:
            # Send message via Kafka (including attachments if present)
            response = await kafka_service.send_chat_message(
                session_id, request.message, attachments=request.attachments
            )

        # Extract message from response
        agent_response = response.get("agent_response", {})
        content = agent_response.get("content", "")

        # Check if a task was created
        task_id = response.get("run_id")

        # Store conversation messages in communication service
        await store_chat_messages(
            conversation_id=conversation_id,
            user_request=request.model_dump(),
            agent_response=content,  # Empty since we're only storing user message initially
            user_id=user_id,
        )

        logger.info(
            "Chat message processed and stored successfully",
            extra={
                "session_id": session_id,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "task_id": task_id,
                "operation": "send_chat_message",
            },
        )

        return ChatMessageResponse(message=content, task_id=task_id)
    except Exception as e:

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to send chat message: {str(e)}")


@router.post("/sessions/{session_id}/chat/quick", response_model=QuickChatMessageResponse)
async def send_quick_chat_message(
    request: ChatMessageRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent with quick response and background processing.

    This endpoint provides a quick response by immediately returning a processing ID
    while the actual message processing happens in the background. This is ideal for
    scenarios where you need to provide immediate feedback to users while the agent
    processes the message asynchronously.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)
    - **attachments**: Optional list of file attachments (images, documents, etc.)

    ## Response
    Returns immediately with a processing ID that can be used to check the status
    and retrieve the agent's response when processing is complete.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat/quick
    {
        "message": "Can you analyze the sales data from last quarter?",
        "attachments": [
            {
                "file_name": "sales_data.csv",
                "file_type": "text/csv",
                "file_size": 1024,
                "file_url": "https://example.com/files/sales_data.csv",
                "metadata": {"description": "Q4 sales data"}
            }
        ]
    }
    ```

    ## Background Processing
    The message is processed asynchronously in the background:
    1. Message is queued for processing
    2. Agent processes the message via Kafka
    3. Response is stored in communication service
    4. Status is updated and can be checked via processing ID

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        # Start background processing (including attachments if present)
        processing_id = await background_task_manager.start_chat_message_processing(
            session_id=session_id,
            message=request.message,
            conversation_id=conversation_id,
            user_id=user_id,
            attachments=request.attachments,
            mode=request.mode,
            tools=request.tools,
            resource=request.resource.value,
        )

        # Store conversation messages in communication service
        await store_chat_messages(
            conversation_id=conversation_id,
            user_request=request.model_dump(),
            agent_response="",  # Empty since we're only storing user message initially
            user_id=user_id,
        )

        logger.info(
            "Quick chat message queued for background processing",
            extra={
                "processing_id": processing_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "operation": "send_quick_chat_message",
            },
        )

        return QuickChatMessageResponse(
            success=True,
            message="Message sent successfully and is being processed",
            processing_id=processing_id,
            session_id=session_id,
        )

    except Exception as e:
        logger.error(
            f"Error queuing quick chat message: {str(e)}",
            extra={
                "session_id": session_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "operation": "send_quick_chat_message",
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to queue chat message: {str(e)}")


@router.get("/processing/{processing_id}/status", response_model=MessageProcessingStatus)
async def get_message_processing_status(
    processing_id: str = Path(
        ...,
        description="Unique identifier of the processing to check",
        example="proc_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Get the current status of a message being processed in the background.

    This endpoint allows you to check the status of a message that was submitted
    for background processing via the quick chat endpoint. It returns the current
    processing status and the agent's response when available.

    ## Path Parameters
    - **processing_id**: The unique identifier returned from the quick chat endpoint

    ## Response
    Returns the current processing status with the following possible states:
    - **processing**: Message is currently being processed
    - **completed**: Processing completed successfully with agent response
    - **failed**: Processing failed with error details

    ## Example
    ```
    GET /api/v1/agents/processing/proc_123/status
    ```

    ## Errors
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Processing ID not found
    - 500: Server error
    """

    try:
        # Get processing status
        status_data = await background_task_manager.get_processing_status(processing_id)

        if not status_data:
            raise HTTPException(status_code=404, detail="Processing ID not found")

        logger.info(
            "Processing status retrieved",
            extra={
                "processing_id": processing_id,
                "status": status_data.get("status"),
                "operation": "get_message_processing_status",
            },
        )

        return MessageProcessingStatus(**status_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving processing status: {str(e)}",
            extra={
                "processing_id": processing_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "operation": "get_message_processing_status",
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to get processing status: {str(e)}")


@router.get("/sessions/{session_id}/chat/stream")
async def send_streaming_chat_message(
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    # current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent with streaming response.

    This endpoint allows you to send a message to an agent and receive a streaming
    response in real-time. The response is delivered as Server-Sent Events (SSE),
    allowing for real-time interaction with the agent as it generates its response.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)
    - **metadata**: Optional metadata for the streaming request

    ## Response
    Returns a streaming response using Server-Sent Events (SSE) format.
    Each event contains a JSON object with chunk data.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat/stream
    {
        "message": "Can you explain machine learning step by step?",
        "metadata": {
            "stream_format": "sse",
            "include_timestamps": true
        }
    }
    ```

    ## Stream Format
    The response stream follows the SSE format:
    ```
    data: {"content": "Machine learning is...", "chunk_type": "text", "is_final": false}
    data: {"content": " a subset of AI...", "chunk_type": "text", "is_final": false}
    data: [DONE]
    ```

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:

        # Create a queue for this client to receive SSE events with enhanced size
        client_queue = queue.Queue(200)  # Increased from 100 to 200
        client_id = session_id

        def event_stream():
            """
            Generator function to stream events to the client.
            """
            try:
                # Add the client to the SSE manager
                new_client_queue = sse_manager.add_client(client_id, client_queue)

                while not sse_manager.is_shutting_down:
                    try:
                        # Wait for an event from the queue
                        event = new_client_queue.get(timeout=10)
                        # Only process events meant for this client or broadcast events
                        if event.get("client_id") is None or event.get("client_id") == client_id:
                            yield (
                                f"event: {event['event']}\ndata: {json.dumps(event['data'])}"
                                f"\ntype: {event['type']}\nid: {event['timestamp']}\n\n"
                            )
                    except queue.Empty:
                        # Send a keep-alive event if no events are received
                        yield "event: keep-alive\ndata: keep-alive\n\n"

            except GeneratorExit:
                # Handle client disconnection
                logger.info(f"Client {client_id} connection closed")
            finally:
                # Remove the client from the SSE manager
                sse_manager.remove_client(client_id)

        # Return a streaming response with the event stream
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # Disable nginx buffering
            },
        )

    except HTTPException:
        # Clean up on HTTP exceptions
        sse_manager.remove_client(client_id)
        raise
    except Exception as e:
        # Clean up on other exceptions
        sse_manager.remove_client(client_id)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start streaming chat: {str(e)}",
        )


@router.post("/sessions/{session_id}/chat/stop-stream")
async def stop_streaming_chat_response(
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Stop an active streaming chat response for a session.

    This endpoint allows you to stop an ongoing streaming response from an agent
    within a specific session. This is useful when users want to interrupt a
    long-running response or when they need to terminate the stream early.

    ## Path Parameters
    - **session_id**: The unique identifier of the session with active streaming

    ## Response
    Returns confirmation that the stream stop request was processed.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat/stop-stream
    ```

    ## Use Cases
    - **User interruption**: User wants to stop a long response
    - **Session cleanup**: Terminate streams before closing sessions
    - **Resource management**: Stop unnecessary streaming to save resources
    - **Error recovery**: Stop problematic streams that may be stuck

    ## Errors
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """
    user_id = current_user.get("user_id")

    try:
        logger.info(
            "Stop stream response request received",
            extra={
                "user_id": user_id,
                "session_id": session_id,
                "operation": "stop_streaming_chat_response",
            },
        )

        # Send stop stream request via Kafka
        response = await kafka_service.stop_agent_chat_stream(
            session_id=session_id,
            user_id=user_id,
        )

        logger.info(
            "Stream stop request sent successfully",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "run_id": response.get("run_id"),
                "operation": "stop_streaming_chat_response",
            },
        )

        return {
            "success": True,
            "message": "Stream stop request processed successfully",
            "session_id": session_id,
            "run_id": response.get("run_id"),
        }

    except Exception as e:
        logger.error(
            f"Error processing stop stream request: {str(e)}",
            extra={
                "user_id": user_id,
                "session_id": session_id,
                "operation": "stop_streaming_chat_response",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop stream response: {str(e)}",
        )


@router.post("/{agent_id}/sessions/{session_id}/tasks", response_model=ExecuteTaskResponse)
async def execute_task(
    request: ExecuteTaskRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
) -> ExecuteTaskResponse:
    """
    Execute a specific task with an agent within an existing session.

    This endpoint allows you to execute structured tasks with an agent. Tasks are
    specific operations that agents can perform, such as data analysis, content
    generation, or complex processing workflows. The task is executed within the
    context of the session.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to execute the task
    - **session_id**: The unique identifier of the session context

    ## Request Body
    - **task_type**: The type of task to execute (e.g., "data_analysis", "text_generation")
    - **task_input**: Input parameters and data for the task

    ## Response
    Returns the task execution result with status and output data.

    ## Example
    ```
    POST /api/v1/agents/agent_123/sessions/session_456/tasks
    {
        "task_type": "data_analysis",
        "task_input": {
            "data": [1, 2, 3, 4, 5],
            "analysis_type": "statistical_summary"
        }
    }
    ```

    ## Errors
    - 400: Invalid request (missing task_type, invalid task_input)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent or session not found
    - 422: Invalid task parameters
    - 500: Server error
    """
    # Generate a request ID
    request_id = get_request_id()

    try:
        # Get user ID
        user_id = current_user.get("id")

        # Prepare task
        task = {
            "type": request.task_type,
            "input": request.task_input,
            "user_id": user_id,  # Add user ID to task
        }

        # Execute task via Kafka
        response = await kafka_service.execute_agent_task(agent_id, session_id, task)

        # Extract result from response
        task_id = response.get("task_id", str(uuid.uuid4()))
        status = response.get("status", {}).get("state", "completed")
        result = response.get("result")

        return ExecuteTaskResponse(
            task_id=task_id,
            status=status,
            result=result,
        )
    except Exception as e:

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to execute task: {str(e)}")


@router.post("/{agent_id}/run-task", response_model=RunAgentTaskResponse)
async def run_agent_task(
    request: RunAgentTaskRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Run a task directly with an agent without session management.

    This endpoint provides a simplified interface for executing tasks with an agent
    without the need to create and manage sessions. It's ideal for stateless operations
    where you don't need conversation context or session persistence.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to run the task

    ## Request Body
    - **agent_id**: The agent identifier (must match path parameter)
    - **task_type**: The type of task to execute
    - **task_input**: Input parameters and data for the task

    ## Response
    Returns the task execution result with status and output data.

    ## Example
    ```
    POST /api/v1/agents/agent_123/run-task
    {
        "agent_id": "agent_123e4567-e89b-12d3-a456-************",
        "task_type": "text_generation",
        "task_input": {
            "prompt": "Write a summary of renewable energy benefits",
            "max_length": 500
        }
    }
    ```

    ## Errors
    - 400: Invalid request (missing task_type, invalid task_input)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 422: Invalid task parameters
    - 500: Server error
    """
    request_id = get_request_id()

    try:
        user_id = current_user.get("user_id", current_user.get("id"))

        task = {
            "type": request.task_type,
            "input": request.task_input,
            "user_id": user_id,
        }

        # Send task request via Kafka and receive chat response
        response = await kafka_service.execute_agent_task(agent_id, task)

        task_id = response.get("task_id", str(uuid.uuid4()))
        status = response.get("status", {}).get("state", "completed")
        result = response.get("result")

        return RunAgentTaskResponse(
            task_id=task_id,
            status=status,
            result=result,
        )
    except Exception as e:

        raise HTTPException(status_code=500, detail=f"Failed to run agent task: {str(e)}")


@router.get("/tasks/{task_id}/status", response_model=AgentTaskStatus)
async def get_task_status(
    task_id: str = Path(
        ...,
        description="Unique identifier of the task",
        example="task_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Get the current status and progress of an agent execution task.

    This endpoint allows you to check the status of a previously submitted task
    using the task ID returned from task execution endpoints. It provides real-time
    status updates and progress information.

    ## Path Parameters
    - **task_id**: The unique identifier of the task to check

    ## Response
    Returns the current task status, progress percentage, and estimated completion time.

    ## Status Values
    - **submitted**: Task has been submitted and is queued for processing
    - **running**: Task is currently being processed by the agent
    - **completed**: Task has completed successfully
    - **failed**: Task has failed with an error
    - **cancelled**: Task was cancelled by the user

    ## Example
    ```
    GET /api/v1/agents/tasks/task_123/status
    ```

    ## Errors
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Task not found or access denied
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("id")

    try:
        logger.info(
            "Task status request received",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "operation": "get_task_status",
            },
        )

        # TODO: Implement actual task status retrieval
        # This would typically involve querying a task status store or cache
        # For now, return a mock response

        # In a real implementation, you would:
        # 1. Query the task status from Redis/database
        # 2. Check if the user has permission to view this task
        # 3. Return the actual status and progress

        response = AgentTaskStatus(
            task_id=task_id,
            status="completed",  # Mock status
            progress=100.0,
            estimated_completion_time=None,
        )

        logger.info(
            "Task status retrieved successfully",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "status": response.status,
                "operation": "get_task_status",
            },
        )

        return response

    except Exception as e:
        logger.error(
            "Failed to retrieve task status",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "error": str(e),
                "operation": "get_task_status",
            },
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve task status: {str(e)}",
        )


@router.post("/{agent_id}/query", response_model=AgentQueryResponse)
async def query_agent(
    request: AgentQueryRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent to query",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Query an agent directly without creating a session.

    This endpoint allows you to send a query directly to an agent and receive a response
    without the need to create and manage a session. It's ideal for simple, one-off queries
    that don't require maintaining conversation state or context.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to query

    ## Request Body
    - **query**: The query text to send to the agent (1-10,000 characters)
    - **user_id**: Optional user ID (defaults to authenticated user if not provided)
    - **organization_id**: Optional organization context
    - **variables**: Optional key-value pairs to pass to the agent

    ## Response
    Returns the agent's response with execution metadata including success status and run ID.

    ## Example
    ```
    POST /api/v1/agents/agent_123/query
    {
        "query": "What are the benefits of renewable energy?",
        "variables": {
            "context": "business",
            "format": "bullet_points"
        }
    }
    ```

    ## Errors
    - 400: Invalid request (empty query, query too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 422: Invalid query parameters
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("id")

    try:
        # Use the authenticated user's ID if not specified in request
        query_user_id = request.user_id or user_id

        logger.info(
            "Agent query request received",
            extra={
                "request_id": request_id,
                "user_id": query_user_id,
                "agent_id": agent_id,
                "query_length": len(request.query),
                "operation": "query_agent",
            },
        )

        # Send query via Kafka
        response = await kafka_service.query_agent(
            agent_id=agent_id,
            query=request.query,
            user_id=query_user_id,
            organization_id=request.organization_id,
            variables=request.variables,
        )

        # Extract response data
        success = response.get("success", False)
        message = response.get("message", "Query processed")
        agent_response = response.get("agent_response")
        run_id = response.get("run_id", request_id)
        final = response.get("final", True)

        logger.info(
            "Agent query processed successfully",
            extra={
                "request_id": request_id,
                "user_id": query_user_id,
                "agent_id": agent_id,
                "run_id": run_id,
                "success": success,
                "operation": "query_agent",
            },
        )

        return AgentQueryResponse(
            success=success,
            message=message,
            agent_response=agent_response,
            run_id=run_id,
            final=final,
        )

    except Exception as e:
        logger.error(
            "Failed to process agent query",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "agent_id": agent_id,
                "error": str(e),
                "operation": "query_agent",
            },
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process agent query: {str(e)}",
        )


@router.post("/sessions/{session_id}/chat-enhanced", response_model=EnhancedChatResponse)
async def send_enhanced_chat_message(
    request: EnhancedChatRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send an enhanced chat message with full conversation context to an agent.

    This endpoint allows you to send a message to an agent along with the full
    conversation context. It's useful for maintaining conversation continuity
    and providing the agent with complete context for better responses.

    ## Path Parameters
    - **session_id**: The unique identifier of the session

    ## Request Body
    - **chat_context**: Array of conversation messages with role and content
    - **user_id**: Optional user ID (defaults to authenticated user)
    - **organization_id**: Optional organization context

    ## Response
    Returns enhanced response with success status, message, and run metadata.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat-enhanced
    {
        "chat_context": [
            {"role": "user", "content": "What is machine learning?"},
            {"role": "assistant", "content": "Machine learning is..."},
            {"role": "user", "content": "Can you give me examples?"}
        ]
    }
    ```

    ## Errors
    - 400: Invalid request (empty context, invalid message format)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """
    request_id = get_request_id()

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        logger.info(
            "Sending enhanced chat message",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "message_count": len(request.chat_context),
                "operation": "send_enhanced_chat_message",
            },
        )

        # Prepare chat request for Kafka
        chat_request = {
            "session_id": session_id,
            "chat_context": [
                {"role": msg.role, "content": msg.content} for msg in request.chat_context
            ],
        }

        # Send via Kafka using the existing method but with enhanced structure
        from app.services.kafka_service import KAFKA_AGENT_CHAT_TOPIC

        correlation_id = await kafka_service.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request)

        # Wait for response
        response = await kafka_service.wait_for_response(correlation_id)

        # Extract response data
        success = response.get("success", True)
        message = response.get("message", "Chat message processed")
        agent_response = response.get("agent_response")
        run_id = response.get("run_id", correlation_id)
        final = response.get("final", True)

        # Extract the last user message and agent response for storage
        if request.chat_context:
            last_user_message = None
            for msg in reversed(request.chat_context):
                if msg.role == "user":
                    last_user_message = msg.content
                    break

            # Store conversation messages in communication service
            agent_content = (
                agent_response.get("content", "")
                if isinstance(agent_response, dict)
                else str(agent_response)
            )
            await store_chat_messages(
                conversation_id=conversation_id,
                user_request=last_user_message,
                agent_response=agent_content,
                user_id=user_id,
            )

        logger.info(
            "Enhanced chat message sent and stored successfully",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "run_id": run_id,
                "success": success,
                "operation": "send_enhanced_chat_message",
            },
        )

        return EnhancedChatResponse(
            success=success,
            message=message,
            agent_response=agent_response,
            run_id=run_id,
            final=final,
        )

    except Exception as e:
        logger.error(
            f"Error sending enhanced chat message: {str(e)}",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "operation": "send_enhanced_chat_message",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500, detail=f"Failed to send enhanced chat message: {str(e)}"
        )


# Orchestration Team Endpoints
@router.post("/orchestration/sessions", response_model=OrchestrationTeamSessionResponse)
async def create_orchestration_team_session(
    request: OrchestrationTeamSessionRequest,
    current_user: dict = Depends(role_required(["user"])),
) -> OrchestrationTeamSessionResponse:
    """
    Create a new orchestration team session for multi-agent collaboration.

    This endpoint creates a session with the global agent orchestration team,
    which enables multiple specialized agents to work together on complex problems
    with optional human-in-the-loop capabilities.

    ## Request Body
    - **conversation_id**: Optional conversation ID to initialize with existing history
    - **variables**: Variables to pass to the orchestration team

    ## Response
    Returns a session identifier for subsequent orchestration team interactions.

    ## Example
    ```
    POST /api/v1/agents/orchestration/sessions
    {
        "conversation_id": "conv_123e4567-e89b-12d3-a456-************",
        "variables": {"domain": "software_development", "complexity": "high"}
    }
    ```

    ## Features
    - **Multi-agent collaboration**: Multiple specialized agents work together
    - **Human-in-the-loop**: Optional human guidance for complex decisions
    - **Conversation continuity**: Initialize with existing conversation history
    - **Configurable iterations**: Control when human input is required

    ## Errors
    - 400: Invalid request (invalid parameters)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("user_id")

    try:
        logger.info(
            "Creating orchestration team session",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "conversation_id": request.conversation_id,
                "operation": "create_orchestration_session",
            },
        )

        # Get conversation messages if conversation_id is provided
        conversation_messages = []
        if request.conversation_id:
            try:

                # Get conversation messages
                messages_data = await communication_service.list_messages(
                    request.conversation_id, page=1, limit=100
                )

                # Convert messages to the format expected by orchestration team
                for message in messages_data.data:
                    role = "user" if message.senderType == "USER" else "assistant"
                    # Extract content from data field instead of non-existent content field
                    content = message.data.get("message", "") if message.data else ""
                    conversation_messages.append({"role": role, "content": content})

                logger.info(
                    f"Loaded {len(conversation_messages)} messages from conversation {request.conversation_id}"
                )

            except Exception as e:
                logger.warning(
                    f"Failed to load conversation {request.conversation_id}: {e}. Creating session without history."
                )

        # Create orchestration team session via Kafka
        session_id = await kafka_service.create_orchestration_team_session(
            user_id=user_id,
            conversation_messages=conversation_messages,
            variables=request.variables,
        )

        # Store session data in Redis for quick lookup
        session_key = f"session:{session_id}"
        session_data = {
            "conversation_id": request.conversation_id,
            "user_id": user_id,
            "variables": request.variables,
            "session_type": "orchestration_team",
        }
        redis_service.set_data_to_redis(session_key, "data", session_data)  # 24 hours

        logger.info(
            "Orchestration team session created successfully",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "conversation_id": request.conversation_id,
                "operation": "create_orchestration_session",
            },
        )

        return OrchestrationTeamSessionResponse(
            session_id=session_id,
            message="Orchestration team session created successfully",
            success=True,
        )

    except Exception as e:
        logger.error(
            f"Error creating orchestration team session: {str(e)}",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "conversation_id": request.conversation_id,
                "operation": "create_orchestration_session",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500, detail=f"Failed to create orchestration team session: {str(e)}"
        )


@router.post(
    "/orchestration/sessions/{session_id}/chat", response_model=OrchestrationTeamChatResponse
)
async def send_orchestration_team_message(
    request: OrchestrationTeamChatRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the orchestration team session",
        example="orch_session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
) -> OrchestrationTeamChatResponse:
    """
    Send a message to the orchestration team for collaborative processing.

    This endpoint sends a message to the orchestration team, which consists of
    multiple specialized agents working together to provide comprehensive solutions.
    The team can request human input for complex decisions when needed.

    ## Path Parameters
    - **session_id**: The orchestration team session identifier

    ## Request Body
    - **message**: The message content to send to the orchestration team
    - **attachments**: Optional file attachments

    ## Response
    Contains the team's collaborative response and processing metadata.

    ## Example
    ```
    POST /api/v1/agents/orchestration/sessions/orch_session_123/chat
    {
        "message": "I need help designing a scalable microservices architecture for an e-commerce platform.",
        "attachments": [
            {
                "file_name": "requirements.pdf",
                "file_type": "application/pdf",
                "file_size": 2048,
                "file_data": "base64_encoded_content...",
                "metadata": {"description": "System requirements document"}
            }
        ],
    }
    ```

    ## Team Collaboration Features
    - **Multi-agent expertise**: Architecture, security, performance, and other specialists
    - **Iterative refinement**: Agents collaborate to improve solutions
    - **Human guidance**: Request human input for complex decisions
    - **Comprehensive analysis**: Multiple perspectives on complex problems

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("user_id")

    try:
        # Validate session exists and belongs to user
        session_key = f"session:{session_id}"
        session_data = redis_service.get_data_from_redis(session_key, "data")

        if not session_data or session_data.get("user_id") != user_id:
            raise HTTPException(
                status_code=404,
                detail="Orchestration team session not found or access denied",
            )

        logger.info(
            "Sending message to orchestration team",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "message_length": len(request.message),
                "attachments_count": len(request.attachments) if request.attachments else 0,
                "operation": "orchestration_team_chat",
            },
        )

        # Send message to orchestration team via Kafka
        response = await kafka_service.send_orchestration_team_message(
            session_id=session_id,
            message=request.message,
            attachments=request.attachments,
            mode=request.mode,
            tools=request.tools,
            resource=request.resource.value,
        )

        logger.info(
            "Message sent to orchestration team successfully",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "run_id": response.get("run_id"),
                "operation": "orchestration_team_chat",
            },
        )

        return OrchestrationTeamChatResponse(
            message="Message sent to orchestration team successfully",
            success=response.get("success", True),
            run_id=response.get("run_id", ""),
            team_response=response.get("agent_response"),
            final=response.get("final", False),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error sending message to orchestration team: {str(e)}",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "operation": "orchestration_team_chat",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500,
            detail=f"Failed to send message to orchestration team: {str(e)}",
        )


@router.post("/orchestration/sessions/{session_id}/human-input")
async def send_human_input_response(
    request: HumanInputResponse,
    session_id: str = Path(
        ...,
        description="Unique identifier of the orchestration team session",
        example="orch_session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
) -> dict:
    """
    Provide human input response to the orchestration team.

    This endpoint allows users to respond to human input requests from the
    orchestration team during collaborative problem-solving sessions.

    ## Path Parameters
    - **session_id**: The orchestration team session identifier

    ## Request Body
    - **team_conversation_id**: The team conversation ID requiring input
    - **user_input**: The human's input or decision
    - **selected_option**: If options were provided, the selected option

    ## Response
    Confirmation that the human input was received and processed.

    ## Example
    ```
    POST /api/v1/agents/orchestration/sessions/orch_session_123/human-input
    {
        "team_conversation_id": "team_conv_123e4567-e89b-12d3-a456-************",
        "user_input": "I prefer Approach A with API Gateway, but please ensure it includes proper security measures.",
        "selected_option": "Approach A: API Gateway"
    }
    ```

    ## Use Cases
    - **Decision guidance**: Choose between multiple technical approaches
    - **Requirement clarification**: Provide additional context or constraints
    - **Priority setting**: Help the team prioritize different aspects
    - **Approval workflows**: Approve or reject proposed solutions

    ## Errors
    - 400: Invalid request (missing required fields)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or access denied
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("user_id")

    try:
        # Validate session exists and belongs to user
        session_key = f"session:{session_id}"
        session_data = redis_service.get_data_from_redis(session_key, "data")

        if not session_data or session_data.get("user_id") != user_id:
            raise HTTPException(
                status_code=404,
                detail="Orchestration team session not found or access denied",
            )

        logger.info(
            "Sending human input response to orchestration team",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "team_conversation_id": request.team_conversation_id,
                "operation": "human_input_response",
            },
        )

        # Send human input response via Kafka
        response = await kafka_service.send_human_input_response(
            session_id=session_id,
            team_conversation_id=request.team_conversation_id,
            user_input=request.user_input,
        )

        logger.info(
            "Human input response sent successfully",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "team_conversation_id": request.team_conversation_id,
                "run_id": response.get("run_id"),
                "operation": "human_input_response",
            },
        )

        return {
            "success": True,
            "message": "Human input response sent successfully",
            "run_id": response.get("run_id"),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error sending human input response: {str(e)}",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "team_conversation_id": request.team_conversation_id,
                "operation": "human_input_response",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500,
            detail=f"Failed to send human input response: {str(e)}",
        )
