from typing import Any, Dict, List

from fastapi import APIRouter

from app.services.health_check_service import HealthService

health_router = APIRouter(tags=["health"])


@health_router.get(
    "/health",
    summary="Get basic health status",
    description="Returns a simple health status of the Developer API Gateway",
    response_model=Dict[str, str],
)
async def health_check() -> Dict[str, str]:
    """Basic health check endpoint."""
    return {"status": "healthy", "service": "developer-api-gateway"}


@health_router.get(
    "/health/services",
    summary="Get health status of all services",
    description="Checks the health of all microservices and returns their status",
    response_model=Dict[str, Any],
)
async def check_all_services() -> Dict[str, Any]:
    """Check the health of all microservices."""
    health_service = HealthService()
    return await health_service.check_all_services()


@health_router.get(
    "/health/services/{service_name}",
    summary="Get health status of a specific service",
    description="Checks the health of a specific microservice and returns its status",
    response_model=Dict[str, Any],
)
async def check_specific_service(service_name: str) -> Dict[str, Any]:
    """
    Check the health of a specific microservice.

    Args:
        service_name: Name of the service to check (user, admin, etc.)

    Returns:
        Dict[str, Any]: Health status of the specified service
    """
    health_service = HealthService()
    return await health_service.check_specific_service(service_name)
