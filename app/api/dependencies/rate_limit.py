"""
Optimized rate limiting dependencies with Redis backend and advanced features.

This module provides enhanced rate limiting with:
- Redis-based distributed rate limiting
- Sliding window algorithm
- Tiered rate limits based on user type
- Rate limit analytics
- Graceful degradation
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, Callable, Awaitable, <PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass

from fastapi import Depends, HTTPException, Request, status
import redis.asyncio as redis

from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)


class UserTier(Enum):
    """User tier enumeration for rate limiting."""

    FREE = "free"
    BASIC = "basic"
    PRO = "pro"
    ENTERPRISE = "enterprise"


@dataclass
class RateLimitConfig:
    """Rate limit configuration for different tiers."""

    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int  # Maximum burst requests allowed


# Rate limit configurations by tier
RATE_LIMIT_CONFIGS = {
    UserTier.FREE: RateLimitConfig(
        requests_per_minute=10, requests_per_hour=100, requests_per_day=1000, burst_limit=20
    ),
    UserTier.BASIC: RateLimitConfig(
        requests_per_minute=50, requests_per_hour=1000, requests_per_day=10000, burst_limit=100
    ),
    UserTier.PRO: RateLimitConfig(
        requests_per_minute=200, requests_per_hour=5000, requests_per_day=100000, burst_limit=400
    ),
    UserTier.ENTERPRISE: RateLimitConfig(
        requests_per_minute=1000,
        requests_per_hour=50000,
        requests_per_day=1000000,
        burst_limit=2000,
    ),
}


class OptimizedRateLimiter:
    """
    Optimized rate limiter with Redis backend and sliding window algorithm.
    """

    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self._fallback_store: Dict[str, Dict[str, Any]] = {}
        self._redis_available = True

    async def initialize(self):
        """Initialize Redis connection."""
        try:
            if settings.REDIS_URI:
                self.redis_client = redis.from_url(
                    settings.REDIS_URI,
                    encoding="utf-8",
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30,
                )
                # Test connection
                await self.redis_client.ping()
                logger.info("Redis connection established for rate limiting")
            else:
                logger.warning("Redis URI not configured, using in-memory fallback")
                self._redis_available = False
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}. Using in-memory fallback")
            self._redis_available = False

    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()

    def _get_rate_limit_keys(self, identifier: str) -> Dict[str, str]:
        """Generate Redis keys for different time windows."""
        return {
            "minute": f"rate_limit:minute:{identifier}",
            "hour": f"rate_limit:hour:{identifier}",
            "day": f"rate_limit:day:{identifier}",
            "burst": f"rate_limit:burst:{identifier}",
        }

    def _get_identifier(self, request: Request, api_key: Optional[str] = None) -> str:
        """Get unique identifier for rate limiting."""
        if api_key:
            return f"api_key:{api_key}"
        return f"ip:{request.client.host}"

    async def _sliding_window_check(
        self, key: str, window_size: int, limit: int
    ) -> Tuple[bool, int, int]:
        """
        Sliding window rate limit check using Redis.

        Returns:
            (is_allowed, current_count, reset_time)
        """
        if not self._redis_available or not self.redis_client:
            return await self._fallback_sliding_window_check(key, window_size, limit)

        try:
            current_time = int(time.time())
            window_start = current_time - window_size

            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()

            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)

            # Count current entries
            pipe.zcard(key)

            # Add current request
            pipe.zadd(key, {str(current_time): current_time})

            # Set expiration
            pipe.expire(key, window_size + 60)  # Extra buffer

            results = await pipe.execute()
            current_count = results[1] + 1  # +1 for the request we just added

            is_allowed = current_count <= limit
            reset_time = current_time + window_size

            return is_allowed, current_count, reset_time

        except Exception as e:
            logger.error(f"Redis sliding window check failed: {e}")
            return await self._fallback_sliding_window_check(key, window_size, limit)

    async def _fallback_sliding_window_check(
        self, key: str, window_size: int, limit: int
    ) -> Tuple[bool, int, int]:
        """Fallback sliding window check using in-memory store."""
        current_time = int(time.time())
        window_start = current_time - window_size

        # Get or create window data
        if key not in self._fallback_store:
            self._fallback_store[key] = []

        # Remove old entries
        self._fallback_store[key] = [
            timestamp for timestamp in self._fallback_store[key] if timestamp > window_start
        ]

        # Add current request
        self._fallback_store[key].append(current_time)

        current_count = len(self._fallback_store[key])
        is_allowed = current_count <= limit
        reset_time = current_time + window_size

        return is_allowed, current_count, reset_time

    async def check_rate_limit(
        self, request: Request, api_key: Optional[str] = None, user_tier: UserTier = UserTier.FREE
    ) -> Dict[str, Any]:
        """
        Check rate limits across multiple time windows.

        Returns:
            Dictionary with rate limit status and metadata
        """
        identifier = self._get_identifier(request, api_key)
        keys = self._get_rate_limit_keys(identifier)
        config = RATE_LIMIT_CONFIGS[user_tier]

        # Check all time windows
        checks = await asyncio.gather(
            self._sliding_window_check(keys["minute"], 60, config.requests_per_minute),
            self._sliding_window_check(keys["hour"], 3600, config.requests_per_hour),
            self._sliding_window_check(keys["day"], 86400, config.requests_per_day),
            self._sliding_window_check(keys["burst"], 60, config.burst_limit),
            return_exceptions=True,
        )

        # Process results
        minute_allowed, minute_count, minute_reset = checks[0]
        hour_allowed, hour_count, hour_reset = checks[1]
        day_allowed, day_count, day_reset = checks[2]
        burst_allowed, burst_count, burst_reset = checks[3]

        # Determine if request is allowed
        is_allowed = all([minute_allowed, hour_allowed, day_allowed, burst_allowed])

        # Find the most restrictive limit
        limits_info = [
            ("minute", minute_allowed, minute_count, config.requests_per_minute, minute_reset),
            ("hour", hour_allowed, hour_count, config.requests_per_hour, hour_reset),
            ("day", day_allowed, day_count, config.requests_per_day, day_reset),
            ("burst", burst_allowed, burst_count, config.burst_limit, burst_reset),
        ]

        # Find the first violated limit or the most restrictive one
        violated_limit = None
        for limit_info in limits_info:
            window, allowed, count, limit, reset_time = limit_info
            if not allowed:
                violated_limit = limit_info
                break

        if not violated_limit:
            # Find the most restrictive limit (highest usage percentage)
            violated_limit = max(limits_info, key=lambda x: x[2] / x[3] if x[3] > 0 else 0)

        window, _, count, limit, reset_time = violated_limit

        return {
            "allowed": is_allowed,
            "identifier": identifier,
            "user_tier": user_tier.value,
            "current_usage": {
                "minute": f"{minute_count}/{config.requests_per_minute}",
                "hour": f"{hour_count}/{config.requests_per_hour}",
                "day": f"{day_count}/{config.requests_per_day}",
                "burst": f"{burst_count}/{config.burst_limit}",
            },
            "violated_window": window if not is_allowed else None,
            "retry_after": (max(0, reset_time - int(time.time())) if not is_allowed else 0),
            "reset_time": reset_time,
        }

    async def get_usage_stats(
        self, request: Request, api_key: Optional[str] = None, user_tier: UserTier = UserTier.FREE
    ) -> Dict[str, Any]:
        """Get current usage statistics without incrementing counters."""
        identifier = self._get_identifier(request, api_key)
        keys = self._get_rate_limit_keys(identifier)
        config = RATE_LIMIT_CONFIGS[user_tier]

        if not self._redis_available or not self.redis_client:
            # Fallback implementation
            current_time = int(time.time())
            return {
                "identifier": identifier,
                "user_tier": user_tier.value,
                "limits": {
                    "minute": config.requests_per_minute,
                    "hour": config.requests_per_hour,
                    "day": config.requests_per_day,
                    "burst": config.burst_limit,
                },
                "current_usage": {"minute": 0, "hour": 0, "day": 0, "burst": 0},
            }

        try:
            current_time = int(time.time())

            # Get counts for all windows
            pipe = self.redis_client.pipeline()
            pipe.zcount(keys["minute"], current_time - 60, current_time)
            pipe.zcount(keys["hour"], current_time - 3600, current_time)
            pipe.zcount(keys["day"], current_time - 86400, current_time)
            pipe.zcount(keys["burst"], current_time - 60, current_time)

            results = await pipe.execute()

            return {
                "identifier": identifier,
                "user_tier": user_tier.value,
                "limits": {
                    "minute": config.requests_per_minute,
                    "hour": config.requests_per_hour,
                    "day": config.requests_per_day,
                    "burst": config.burst_limit,
                },
                "current_usage": {
                    "minute": results[0],
                    "hour": results[1],
                    "day": results[2],
                    "burst": results[3],
                },
            }

        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return {"error": "Failed to retrieve usage statistics"}


# Global rate limiter instance
rate_limiter = OptimizedRateLimiter()


async def get_user_tier(api_key: Optional[str] = None) -> UserTier:
    """
    Determine user tier based on API key or other factors.
    This should be replaced with actual user tier lookup logic.
    """
    if not api_key:
        return UserTier.FREE

    # TODO: Implement actual tier lookup from database
    # For now, return based on API key pattern
    if api_key.startswith("ent_"):
        return UserTier.ENTERPRISE
    elif api_key.startswith("pro_"):
        return UserTier.PRO
    elif api_key.startswith("basic_"):
        return UserTier.BASIC
    else:
        return UserTier.FREE


async def rate_limit_dependency(
    request: Request, api_key: Optional[str] = None, user_tier: UserTier = Depends(get_user_tier)
) -> Dict[str, Any]:
    """
    Enhanced rate limiting dependency for FastAPI.

    Args:
        request: The FastAPI request object
        api_key: The API key (optional)
        user_tier: The user tier for rate limiting

    Returns:
        Rate limit information dictionary

    Raises:
        HTTPException: If the rate limit is exceeded
    """
    # Initialize rate limiter if needed
    if not rate_limiter._redis_available and rate_limiter.redis_client is None:
        await rate_limiter.initialize()

    # Check rate limit
    rate_limit_info = await rate_limiter.check_rate_limit(request, api_key, user_tier)

    if not rate_limit_info["allowed"]:
        # Log rate limit exceeded
        logger.warning(
            f"Rate limit exceeded for {rate_limit_info['identifier']} "
            f"(tier: {rate_limit_info['user_tier']}, "
            f"window: {rate_limit_info['violated_window']})"
        )

        # Prepare headers
        headers = {
            "X-RateLimit-Limit": str(RATE_LIMIT_CONFIGS[user_tier].requests_per_minute),
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": str(rate_limit_info["reset_time"]),
            "Retry-After": str(rate_limit_info["retry_after"]),
        }

        # Raise HTTP exception
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Rate limit exceeded",
                "message": (
                    f"Rate limit exceeded for " f"{rate_limit_info['violated_window']} window"
                ),
                "retry_after": rate_limit_info["retry_after"],
                "current_usage": rate_limit_info["current_usage"],
            },
            headers=headers,
        )

    # Add rate limit headers to successful responses
    request.state.rate_limit_headers = {
        "X-RateLimit-Limit": str(RATE_LIMIT_CONFIGS[user_tier].requests_per_minute),
        "X-RateLimit-Remaining": str(
            RATE_LIMIT_CONFIGS[user_tier].requests_per_minute
            - int(rate_limit_info["current_usage"]["minute"].split("/")[0])
        ),
        "X-RateLimit-Reset": str(rate_limit_info["reset_time"]),
    }

    return rate_limit_info


def enhanced_rate_limit(
    user_tier: UserTier = UserTier.FREE,
) -> Callable[[Request, Optional[str]], Awaitable[Dict[str, Any]]]:
    """
    Create an enhanced rate limiting dependency with specific tier.

    Args:
        user_tier: The user tier for rate limiting

    Returns:
        A rate limiting dependency function
    """

    async def dependency(request: Request, api_key: Optional[str] = None) -> Dict[str, Any]:
        return await rate_limit_dependency(request, api_key, user_tier)

    return dependency


# Startup and shutdown handlers
async def startup_rate_limiter():
    """Initialize rate limiter on startup."""
    await rate_limiter.initialize()


async def shutdown_rate_limiter():
    """Cleanup rate limiter on shutdown."""
    await rate_limiter.close()
