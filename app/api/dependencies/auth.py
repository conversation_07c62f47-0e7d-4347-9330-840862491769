"""
Enhanced authentication dependencies with API key validation.

This module provides enhanced authentication dependencies that validate
API keys against the database and support both simple API key auth
and API key + secret key authentication.
"""

import logging
from typing import Optional, Dict, Any

from fastapi import Depends, HTTPException, Header, status

from app.services.api_key_service import api_key_service

logger = logging.getLogger(__name__)


async def get_api_key_user(
    x_api_key: str = Header(None, description="API Key for authentication")
) -> Dict[str, Any]:
    """
    Validate API key and return user information.

    Args:
        x_api_key: The API key from the X-API-Key header

    Returns:
        User information associated with the API key

    Raises:
        HTTPException: If the API key is missing or invalid
    """
    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is missing",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    # Validate API key
    user_data = await api_key_service.validate_api_key(x_api_key)
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    logger.info(
        f"API key authenticated for user: {user_data.get('id')} "
        f"(tier: {user_data.get('tier')})"
    )

    return user_data


async def get_api_key_user_with_secret(
    x_api_key: str = Header(None, description="API Key for authentication"),
    x_secret_key: str = Header(None, description="Secret Key for authentication"),
    x_signature: Optional[str] = Header(
        None, description="HMAC signature for request validation"
    ),
    x_timestamp: Optional[str] = Header(
        None, description="Request timestamp for signature validation"
    ),
) -> Dict[str, Any]:
    """
    Validate API key with secret key for enhanced security.

    Args:
        x_api_key: The API key from the X-API-Key header
        x_secret_key: The secret key from the X-Secret-Key header
        x_signature: Optional HMAC signature from X-Signature header
        x_timestamp: Optional timestamp from X-Timestamp header

    Returns:
        User information associated with the API key

    Raises:
        HTTPException: If authentication fails
    """
    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is missing",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    if not x_secret_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Secret key is missing",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    # Validate API key with secret
    user_data = await api_key_service.validate_api_key_with_secret(
        x_api_key, x_secret_key, x_signature, x_timestamp
    )

    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key, secret key, or signature",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    logger.info(
        f"API key with secret authenticated for user: {user_data.get('id')} "
        f"(tier: {user_data.get('tier')}, "
        f"signature_validated: {user_data.get('signature_validated', False)})"
    )

    return user_data


async def get_optional_api_key_user(
    x_api_key: Optional[str] = Header(
        None, description="Optional API Key for authentication"
    )
) -> Optional[Dict[str, Any]]:
    """
    Optionally validate API key if provided.

    Args:
        x_api_key: Optional API key from the X-API-Key header

    Returns:
        User information if API key is valid, None otherwise
    """
    if not x_api_key:
        return None

    try:
        return await get_api_key_user(x_api_key)
    except HTTPException:
        return None


def require_api_key_tier(required_tier: str):
    """
    Create a dependency that requires a specific API key tier.

    Args:
        required_tier: Required tier (free, basic, pro, enterprise)

    Returns:
        Dependency function that validates tier
    """
    tier_hierarchy = {"free": 0, "basic": 1, "pro": 2, "enterprise": 3}

    async def check_tier(
        current_user: Dict[str, Any] = Depends(get_api_key_user)
    ) -> Dict[str, Any]:
        user_tier = current_user.get("tier", "free")
        user_tier_level = tier_hierarchy.get(user_tier, 0)
        required_tier_level = tier_hierarchy.get(required_tier, 0)

        if user_tier_level < required_tier_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"This endpoint requires {required_tier} tier or higher. "
                f"Current tier: {user_tier}",
            )

        return current_user

    return check_tier


def require_active_user():
    """
    Create a dependency that requires an active user.

    Returns:
        Dependency function that validates user is active
    """

    async def check_active(
        current_user: Dict[str, Any] = Depends(get_api_key_user)
    ) -> Dict[str, Any]:
        if not current_user.get("is_active", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive",
            )

        return current_user

    return check_active


# Convenience dependencies for common use cases
async def get_active_api_key_user(
    current_user: Dict[str, Any] = Depends(require_active_user())
) -> Dict[str, Any]:
    """Get active API key user (convenience dependency)."""
    return current_user


async def get_basic_tier_user(
    current_user: Dict[str, Any] = Depends(require_api_key_tier("basic"))
) -> Dict[str, Any]:
    """Get user with basic tier or higher (convenience dependency)."""
    return current_user


async def get_pro_tier_user(
    current_user: Dict[str, Any] = Depends(require_api_key_tier("pro"))
) -> Dict[str, Any]:
    """Get user with pro tier or higher (convenience dependency)."""
    return current_user


async def get_enterprise_tier_user(
    current_user: Dict[str, Any] = Depends(require_api_key_tier("enterprise"))
) -> Dict[str, Any]:
    """Get user with enterprise tier (convenience dependency)."""
    return current_user