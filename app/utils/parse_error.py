import re
from typing import Dict, Any


def parse_error(error_message: str) -> Dict[str, Any]:
    """
    Parse error messages from gRPC services and extract status code and message.
    
    Args:
        error_message: The error message from the gRPC service
        
    Returns:
        Dictionary with code and message
    """
    # Default error response
    error_response = {
        "code": 500,
        "message": "Internal server error"
    }
    
    # Try to extract status code and message from the error
    try:
        # Check for common gRPC error patterns
        status_match = re.search(r'StatusCode\.([A-Z_]+)', error_message)
        if status_match:
            status_name = status_match.group(1)
            
            # Map gRPC status codes to HTTP status codes
            status_map = {
                "OK": 200,
                "CANCELLED": 499,
                "UNKNOWN": 500,
                "INVALID_ARGUMENT": 400,
                "DEADLINE_EXCEEDED": 504,
                "NOT_FOUND": 404,
                "ALREADY_EXISTS": 409,
                "PERMISSION_DENIED": 403,
                "RESOURCE_EXHAUSTED": 429,
                "FAILED_PRECONDITION": 400,
                "ABORTED": 409,
                "OUT_OF_RANGE": 400,
                "UNIMPLEMENTED": 501,
                "INTERNAL": 500,
                "UNAVAILABLE": 503,
                "DATA_LOSS": 500,
                "UNAUTHENTICATED": 401
            }
            
            error_response["code"] = status_map.get(status_name, 500)
        
        # Extract the detailed error message
        detail_match = re.search(r'details="([^"]+)"', error_message)
        if detail_match:
            error_response["message"] = detail_match.group(1)
        else:
            # If no specific detail, use the whole message but clean it up
            cleaned_message = re.sub(r'<.*?>', '', error_message)
            cleaned_message = re.sub(r'\s+', ' ', cleaned_message).strip()
            if cleaned_message:
                error_response["message"] = cleaned_message
    
    except Exception:
        # If parsing fails, return the original error message with a 500 status
        error_response["message"] = error_message
    
    return error_response
