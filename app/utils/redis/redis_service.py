import json
from typing import Any, Optional

from redis.client import Redis

from app.utils.redis.redis_client import RedisClient


class RedisService:
    """
    Service for interacting with Red<PERSON>.
    """

    def __init__(self):
        self.redis_client: Redis[str] = RedisClient().get_client()

    def set_data_to_redis(
        self,
        key: str,
        field: str,
        value: Any,
        expiry: Optional[int] = 7200,
    ) -> bool:
        """
        Set data to Redis.

        Args:
            key: The Redis key
            field: The field name
            value: The value to set
            expiry: Optional expiry time in seconds

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert value to string if it's not already
            if not isinstance(value, str):
                value = json.dumps(value)

            # Set the value in Redis
            self.redis_client.hset(key, field, value)

            # Set expiry if provided
            if expiry:
                self.redis_client.expire(key, expiry)

            return True
        except Exception as e:
            print(f"Error setting data to Redis: {str(e)}")
            return False

    def get_data_from_redis(self, key: str, field: str) -> Any:
        """
        Get data from Redis.

        Args:
            key: The Redis key
            field: The field name

        Returns:
            The value from Redis, or None if not found
        """
        try:
            value = self.redis_client.hget(key, field)
            if value:
                try:
                    # Try to parse as JSON
                    return json.loads(value)
                except json.JSONDecodeError:
                    # Return as is if not JSON
                    return value
            return None
        except Exception as e:
            print(f"Error getting data from Redis: {str(e)}")
            return None

    def delete_data_from_redis(self, key: str, field: Optional[str] = None) -> bool:
        """
        Delete data from Redis.

        Args:
            key: The Redis key
            field: Optional field name. If not provided, the entire key is deleted.

        Returns:
            True if successful, False otherwise
        """
        try:
            if field:
                self.redis_client.hdel(key, field)
            else:
                self.redis_client.delete(key)
            return True
        except Exception as e:
            print(f"Error deleting data from Redis: {str(e)}")
            return False
