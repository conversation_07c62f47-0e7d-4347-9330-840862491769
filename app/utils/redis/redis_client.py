import redis
from app.core.config import settings


class RedisClient:
    """
    Redis client for interacting with Redis.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
            cls._instance.initialize()
        return cls._instance

    def initialize(self):
        """
        Initialize the Redis client.
        """
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD,
            decode_responses=True,
        )

    def get_client(self):
        """
        Get the Redis client instance.

        Returns:
            The Redis client instance
        """
        return self.redis_client
