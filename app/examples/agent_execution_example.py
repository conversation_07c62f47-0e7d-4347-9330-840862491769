"""
Example script demonstrating agent execution API usage.

This script shows how to use the agent execution endpoints to run tasks
with agents via the developer API gateway.
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class AgentExecutionClient:
    """Client for interacting with the agent execution API."""
    
    def __init__(self, base_url: str, api_key: str, auth_token: str):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL of the API gateway
            api_key: API key for authentication
            auth_token: JWT token for user authentication
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.auth_token = auth_token
        self.client = httpx.AsyncClient(
            headers={
                "X-API-Key": api_key,
                "Authorization": f"Bearer {auth_token}",
                "Content-Type": "application/json",
            },
            timeout=30.0
        )
    
    async def execute_agent_task(
        self, 
        agent_id: str, 
        query: str, 
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute a task with an agent.
        
        Args:
            agent_id: ID of the agent to execute the task
            query: The query or task to be processed
            metadata: Optional metadata for the request
            
        Returns:
            Task execution response
        """
        url = f"{self.base_url}/api/v1/agents/{agent_id}/execute"
        payload = {
            "query": query,
            "metadata": metadata or {}
        }
        
        response = await self.client.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of a task.
        
        Args:
            task_id: ID of the task to check
            
        Returns:
            Task status information
        """
        url = f"{self.base_url}/api/v1/agents/tasks/{task_id}/status"
        
        response = await self.client.get(url)
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main example function."""
    # Configuration
    BASE_URL = "http://localhost:8000"
    API_KEY = "your-api-key-here-32-characters-long"
    AUTH_TOKEN = "your-jwt-token-here"
    
    # Create client
    client = AgentExecutionClient(BASE_URL, API_KEY, AUTH_TOKEN)
    
    try:
        print("🚀 Agent Execution API Example")
        print("=" * 50)
        
        # Example 1: Execute a simple query
        print("\n1. Executing a simple query with agent-1...")
        
        task_response = await client.execute_agent_task(
            agent_id="agent-1",
            query="What is the capital of France?",
            metadata={"priority": "high", "timeout": 120}
        )
        
        print(f"✅ Task submitted successfully!")
        print(f"   Task ID: {task_response['task_id']}")
        print(f"   Session ID: {task_response['session_id']}")
        print(f"   Status: {task_response['status']}")
        print(f"   Execution Time: {task_response['execution_time_ms']}ms")
        
        # Example 2: Check task status
        print(f"\n2. Checking status of task {task_response['task_id']}...")
        
        status_response = await client.get_task_status(task_response['task_id'])
        
        print(f"✅ Task status retrieved!")
        print(f"   Status: {status_response['status']}")
        print(f"   Progress: {status_response['progress']}%")
        
        # Example 3: Execute a more complex query
        print("\n3. Executing a complex query with agent-2...")
        
        complex_task = await client.execute_agent_task(
            agent_id="agent-2",
            query="Generate a Python function that calculates the Fibonacci sequence up to n terms",
            metadata={
                "language": "python",
                "complexity": "medium",
                "include_comments": True
            }
        )
        
        print(f"✅ Complex task submitted!")
        print(f"   Task ID: {complex_task['task_id']}")
        print(f"   Status: {complex_task['status']}")
        
        # Example 4: Demonstrate error handling
        print("\n4. Testing error handling with invalid agent...")
        
        try:
            await client.execute_agent_task(
                agent_id="invalid-agent",
                query="This should fail"
            )
        except httpx.HTTPStatusError as e:
            print(f"❌ Expected error occurred: {e.response.status_code}")
            error_detail = e.response.json().get("detail", "Unknown error")
            print(f"   Error: {error_detail}")
        
        print("\n🎉 Example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error occurred: {e}")
    
    finally:
        await client.close()


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
