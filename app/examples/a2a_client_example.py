"""
Example script demonstrating how to use the A2A client to interact with agents.
This script shows various ways to communicate with A2A-compliant agents.
"""

import asyncio
import json
from typing import Any, Dict

# Configure logging using the new logging system
from app.core.logging import get_logger, setup_logging
from app.services.a2a_client import AutoGenA2AClient

setup_logging(default_level="INFO", use_json=False)
logger = get_logger(__name__)


async def discover_and_list_agents(client: AutoGenA2AClient):
    """
    Discover and list all available agents.
    """
    logger.info("🔍 Discovering available agents...")

    agents = await client.discover_agents()

    if not agents:
        logger.warning("No agents found!")
        return []

    logger.info(f"Found {len(agents)} agents:")
    for agent in agents:
        logger.info(f"  - {agent.get('id', 'unknown')}: {agent.get('name', 'Unknown Agent')}")
        logger.info(f"    Description: {agent.get('description', 'No description')}")

    return agents


async def get_agent_details(client: AutoGenA2AClient, agent_id: str):
    """
    Get detailed information about a specific agent.
    """
    logger.info(f"📋 Getting details for agent: {agent_id}")

    agent_card = await client.get_agent_card(agent_id)

    if not agent_card:
        logger.error(f"Could not get agent card for {agent_id}")
        return None

    logger.info(f"Agent Card for {agent_id}:")
    logger.info(f"  Name: {agent_card.name}")
    logger.info(f"  Description: {agent_card.description}")
    logger.info(f"  Version: {agent_card.version}")
    logger.info(f"  URL: {agent_card.url}")
    logger.info(f"  Capabilities: {agent_card.capabilities}")

    if agent_card.skills:
        logger.info(f"  Skills ({len(agent_card.skills)}):")
        for skill in agent_card.skills:
            logger.info(f"    - {skill.name}: {skill.description}")

    return agent_card


async def send_simple_message(client: AutoGenA2AClient, agent_id: str, message: str):
    """
    Send a simple message to an agent and get the response.
    """
    logger.info(f"💬 Sending message to {agent_id}: '{message}'")

    response = await client.send_message(
        agent_id=agent_id, message_text=message, metadata={"example": "simple_message"}
    )

    if response:
        logger.info(f"✅ Response received:")
        logger.info(json.dumps(response, indent=2))

        # Extract the actual message content if available
        if "result" in response and isinstance(response["result"], dict):
            result = response["result"]
            if "parts" in result:
                for part in result["parts"]:
                    if part.get("type") == "text":
                        logger.info(f"📝 Agent response: {part.get('text', '')}")
    else:
        logger.error("❌ Failed to get response")

    return response


async def send_streaming_message(client: AutoGenA2AClient, agent_id: str, message: str):
    """
    Send a message to an agent and stream the response.
    """
    logger.info(f"🌊 Sending streaming message to {agent_id}: '{message}'")

    stream = await client.send_message_streaming(
        agent_id=agent_id, message_text=message, metadata={"example": "streaming_message"}
    )

    if stream:
        logger.info("📡 Streaming response:")
        chunk_count = 0

        async for chunk in stream:
            chunk_count += 1
            logger.info(f"  Chunk {chunk_count}:")
            logger.info(f"    {json.dumps(chunk, indent=4)}")

            # Extract text content from chunk
            if "result" in chunk and isinstance(chunk["result"], dict):
                result = chunk["result"]
                if "parts" in result:
                    for part in result["parts"]:
                        if part.get("type") == "text":
                            logger.info(f"    📝 Text: {part.get('text', '')}")

        logger.info(f"✅ Streaming completed with {chunk_count} chunks")
    else:
        logger.error("❌ Failed to start streaming")


async def demonstrate_task_management(client: AutoGenA2AClient, agent_id: str):
    """
    Demonstrate task management capabilities (if supported by the agent).
    """
    logger.info(f"⚙️ Demonstrating task management with {agent_id}")

    # Send a message that might create a task
    response = await client.send_message(
        agent_id=agent_id,
        message_text="Please process this complex request that might take some time",
        metadata={"example": "task_management"},
    )

    if response and "result" in response:
        result = response["result"]

        # Check if we got a task ID
        if isinstance(result, dict) and "taskId" in result:
            task_id = result["taskId"]
            logger.info(f"📋 Task created with ID: {task_id}")

            # Get task status
            task_status = await client.get_task(agent_id, task_id)
            if task_status:
                logger.info(f"📊 Task status:")
                logger.info(json.dumps(task_status, indent=2))

            # Optionally cancel the task (uncomment if needed)
            # cancel_response = await client.cancel_task(agent_id, task_id)
            # if cancel_response:
            #     logger.info(f"🚫 Task cancellation response:")
            #     logger.info(json.dumps(cancel_response, indent=2))
        else:
            logger.info("ℹ️ Response was direct (no task created)")


async def run_comprehensive_example():
    """
    Run a comprehensive example demonstrating all A2A client capabilities.
    """
    logger.info("🚀 Starting A2A Client Example")

    # Initialize the A2A client
    async with AutoGenA2AClient(base_url="http://localhost:8000/api/v1/a2a") as client:

        # 1. Discover available agents
        agents = await discover_and_list_agents(client)

        if not agents:
            logger.error("No agents available for testing")
            return

        # Use the first available agent for examples
        test_agent_id = agents[0].get("id")
        if not test_agent_id:
            logger.error("No valid agent ID found")
            return

        logger.info(f"🎯 Using agent '{test_agent_id}' for examples")

        # 2. Get agent details
        await get_agent_details(client, test_agent_id)

        # 3. Send simple messages
        await send_simple_message(
            client, test_agent_id, "Hello! Can you help me understand what you can do?"
        )

        await send_simple_message(client, test_agent_id, "What is the capital of France?")

        # 4. Send streaming message
        await send_streaming_message(
            client,
            test_agent_id,
            "Please explain the concept of artificial intelligence in detail.",
        )

        # 5. Demonstrate task management (if supported)
        await demonstrate_task_management(client, test_agent_id)

        # 6. Test with multiple agents if available
        if len(agents) > 1:
            logger.info("🔄 Testing with multiple agents...")
            for agent in agents[1:3]:  # Test up to 2 more agents
                agent_id = agent.get("id")
                if agent_id:
                    await send_simple_message(
                        client,
                        agent_id,
                        f"Hello from the A2A client! I'm testing agent {agent_id}.",
                    )

    logger.info("✅ A2A Client Example completed!")


async def run_simple_example():
    """
    Run a simple example for quick testing.
    """
    logger.info("🚀 Starting Simple A2A Client Example")

    async with AutoGenA2AClient() as client:
        # Discover agents
        agents = await client.discover_agents()

        if agents:
            agent_id = agents[0].get("id")
            if agent_id:
                # Send a simple message
                response = await client.send_message(
                    agent_id=agent_id, message_text="Hello, how are you?"
                )

                if response:
                    logger.info("✅ Got response!")
                    logger.info(json.dumps(response, indent=2))
                else:
                    logger.error("❌ No response received")
            else:
                logger.error("No valid agent ID found")
        else:
            logger.error("No agents found")


if __name__ == "__main__":
    # Choose which example to run
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "simple":
        asyncio.run(run_simple_example())
    else:
        asyncio.run(run_comprehensive_example())
