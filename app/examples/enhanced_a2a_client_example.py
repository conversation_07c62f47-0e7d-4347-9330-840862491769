"""
Enhanced A2A Client Example.

This example demonstrates how to use the enhanced A2A protocol routes
to interact with agents using external A2A clients.
"""

import asyncio
import logging
from typing import Any, Dict

import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedA2AClient:
    """Enhanced A2A client for interacting with agents."""

    def __init__(self, base_url: str, api_key: str):
        """
        Initialize the Enhanced A2A client.

        Args:
            base_url: Base URL of the developer API gateway
            api_key: API key for authentication
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.client = httpx.AsyncClient(
            headers={"X-API-Key": api_key},
            timeout=30.0,
        )

    async def discover_agents(self) -> Dict[str, Any]:
        """
        Discover all available A2A-enabled agents.

        Returns:
            List of available agents with their capabilities
        """
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/a2a/agents")
            response.raise_for_status()
            agents = response.json()

            logger.info(f"Discovered {len(agents)} A2A-enabled agents")
            return agents

        except Exception as e:
            logger.error(f"Error discovering agents: {e}")
            raise

    async def get_agent_details(self, agent_id: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent details with A2A protocol information
        """
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/a2a/agents/{agent_id}")
            response.raise_for_status()
            agent_details = response.json()

            logger.info(f"Retrieved details for agent {agent_id}")
            return agent_details

        except Exception as e:
            logger.error(f"Error getting agent details: {e}")
            raise

    async def get_agent_card(self, agent_id: str) -> Dict[str, Any]:
        """
        Get agent card following A2A protocol specification.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent card in A2A protocol format
        """
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/a2a/agents/{agent_id}/.well-known/agent.json"
            )
            response.raise_for_status()
            agent_card = response.json()

            logger.info(f"Retrieved agent card for {agent_id}")
            return agent_card

        except Exception as e:
            logger.error(f"Error getting agent card: {e}")
            raise

    async def send_message(
        self, agent_id: str, message: str, context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Send a message to an agent using A2A protocol.

        Args:
            agent_id: ID of the agent
            message: Message to send
            context: Optional context for the message

        Returns:
            Agent response
        """
        try:
            # Prepare A2A request
            a2a_request = {
                "jsonrpc": "2.0",
                "id": "msg-001",
                "method": "message/send",
                "params": {
                    "message": {
                        "role": "user",
                        "parts": [{"type": "text", "text": message}],
                    },
                    "context": context or {},
                },
            }

            response = await self.client.post(
                f"{self.base_url}/api/v1/a2a/agents/{agent_id}/a2a",
                json=a2a_request,
            )
            response.raise_for_status()
            result = response.json()

            logger.info(f"Sent message to agent {agent_id}")
            return result

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise

    async def execute_task(self, agent_id: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task using the specified agent.

        Args:
            agent_id: ID of the agent
            task_data: Task data including type, input, and parameters

        Returns:
            Task execution result
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/a2a/agents/{agent_id}/execute-task",
                json=task_data,
            )
            response.raise_for_status()
            result = response.json()

            logger.info(f"Executed task with agent {agent_id}")
            return result

        except Exception as e:
            logger.error(f"Error executing task: {e}")
            raise

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main example function."""
    # Configuration
    BASE_URL = "http://localhost:8000"
    API_KEY = "your-api-key-here"

    # Initialize client
    client = EnhancedA2AClient(BASE_URL, API_KEY)

    try:
        # 1. Discover available agents
        print("=== Discovering A2A Agents ===")
        agents = await client.discover_agents()
        print(f"Found {len(agents)} agents:")
        for agent in agents[:3]:  # Show first 3 agents
            print(f"  - {agent['name']} ({agent['id']})")
            print(f"    Capabilities: {', '.join(agent['capabilities'])}")
            print(f"    Status: {agent['status']}")
            print()

        if not agents:
            print("No agents found. Make sure the service is running.")
            return

        # Use the first agent for examples
        agent_id = agents[0]["id"]
        agent_name = agents[0]["name"]

        # 2. Get detailed agent information
        print(f"=== Getting Details for Agent: {agent_name} ===")
        agent_details = await client.get_agent_details(agent_id)
        print(f"Agent: {agent_details['name']}")
        print(f"Description: {agent_details['description']}")
        print(f"Provider: {agent_details['provider']}")
        print(f"Capabilities: {', '.join(agent_details['capabilities'])}")
        print()

        # 3. Get agent card (A2A protocol format)
        print(f"=== Getting Agent Card for: {agent_name} ===")
        agent_card = await client.get_agent_card(agent_id)
        print(f"A2A Protocol Version: {agent_card['a2a_protocol']['version']}")
        print(
            f"Supported Operations: {', '.join(agent_card['capabilities']['supported_operations'])}"
        )
        print()

        # 4. Send a message using A2A protocol
        print(f"=== Sending Message to Agent: {agent_name} ===")
        message = "Hello! Can you help me with a simple task?"
        message_response = await client.send_message(
            agent_id, message, context={"user_id": "example_user", "session_type": "demo"}
        )

        if "result" in message_response:
            result = message_response["result"]
            response_text = result["message"]["parts"][0]["text"]
            print(f"Agent Response: {response_text}")
            print(f"Session ID: {result.get('session_id', 'N/A')}")
        else:
            print(f"Error: {message_response.get('error', 'Unknown error')}")
        print()

        # 5. Execute a task
        print(f"=== Executing Task with Agent: {agent_name} ===")
        task_data = {
            "type": "text_generation",
            "input": {
                "prompt": "Write a short poem about artificial intelligence",
                "max_length": 100,
            },
            "user_id": "example_user",
            "execution_options": {
                "temperature": 0.7,
                "stream": False,
            },
        }

        task_response = await client.execute_task(agent_id, task_data)
        print(f"Task ID: {task_response['task_id']}")
        print(f"Status: {task_response['status']}")
        if task_response.get("result"):
            print(f"Result: {task_response['result']}")
        print()

        # 6. Demonstrate enhanced agent execution
        print("=== Enhanced Agent Execution Example ===")
        enhanced_client = httpx.AsyncClient(
            headers={"X-API-Key": API_KEY},
            timeout=30.0,
        )

        try:
            # Create enhanced session
            session_request = {
                "agent_id": agent_id,
                "user_id": "example_user",
                "session_metadata": {
                    "client_type": "enhanced_a2a_example",
                    "version": "1.0.0",
                },
            }

            session_response = await enhanced_client.post(
                f"{BASE_URL}/api/v1/agents/sessions",
                json=session_request,
            )
            session_response.raise_for_status()
            session_data = session_response.json()

            print(f"Created enhanced session: {session_data['session_id']}")
            print(f"Agent capabilities: {', '.join(session_data['agent_capabilities'])}")

            # Send enhanced chat message
            chat_request = {
                "message": "What are the benefits of using A2A protocol?",
                "context": {
                    "topic": "A2A protocol",
                    "conversation_type": "educational",
                },
                "stream": False,
            }

            chat_response = await enhanced_client.post(
                f"{BASE_URL}/api/v1/agents/sessions/{session_data['session_id']}/chat",
                json=chat_request,
            )
            chat_response.raise_for_status()
            chat_data = chat_response.json()

            print(f"Enhanced chat response: {chat_data['message'][:100]}...")
            print(f"Execution time: {chat_data['metadata']['execution_time']}s")

        finally:
            await enhanced_client.aclose()

        print("\n=== Example completed successfully! ===")

    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())
