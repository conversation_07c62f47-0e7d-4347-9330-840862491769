#!/usr/bin/env python3
"""
Convert Existing Agents to A2A Protocol

This script helps convert your existing agent configurations to be A2A protocol compatible.
It reads agent configurations from the database (via gRPC) and creates A2A-compatible
agent cards with proper skills, capabilities, and metadata.

Usage:
    python app/scripts/convert_agents_to_a2a.py [--dry-run] [--agent-id AGENT_ID]
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from app.services.agent_service import AgentServiceClient
from app.services.a2a_server import MultiAgentA2AServer
from app.schemas.a2a import AgentCard
from app.core.logging import get_logger

logger = get_logger(__name__)


class AgentA2AConverter:
    """
    Converter for transforming existing agents to A2A protocol compatibility.
    """
    
    def __init__(self):
        self.agent_service = AgentServiceClient()
        self.converted_agents: List[Dict[str, Any]] = []
        
    async def convert_agent_config_to_a2a(self, agent_config: Dict[str, Any]) -> Optional[AgentCard]:
        """
        Convert an agent configuration to A2A format.
        
        Args:
            agent_config: The original agent configuration from your example
            
        Returns:
            A2A-compatible AgentCard or None if conversion fails
        """
        try:
            # Extract basic information
            agent_id = agent_config.get("id")
            name = agent_config.get("name", "Unknown Agent")
            description = agent_config.get("description", "")
            
            # Generate A2A capabilities based on agent configuration
            capabilities = {
                "streaming": True,
                "pushNotifications": True,
                "stateTransitionHistory": False,
                "text_chat": True,
            }
            
            # Add specific capabilities based on agent topic type
            topic_type = agent_config.get("agent_topic_type", "").lower()
            if "video" in topic_type:
                capabilities["video_generation"] = True
            elif "image" in topic_type:
                capabilities["image_generation"] = True
            elif "code" in topic_type:
                capabilities["code_generation"] = True
            elif "data" in topic_type or "analysis" in topic_type:
                capabilities["data_analysis"] = True
            elif "document" in topic_type:
                capabilities["document_processing"] = True
            
            # Generate skills based on agent configuration
            skills = self._generate_skills_from_config(agent_config)
            
            # Determine input/output modes
            input_modes = ["text"]
            output_modes = ["text"]
            
            if "video" in topic_type:
                output_modes.append("video")
            elif "image" in topic_type:
                output_modes.append("image")
            
            # Create A2A-compatible agent card
            agent_card = AgentCard(
                id=agent_id,
                name=name,
                description=description,
                url=f"http://localhost:8000/api/v1/a2a/agents/{agent_id}/a2a",
                version="1.0.0",
                defaultInputModes=input_modes,
                defaultOutputModes=output_modes,
                capabilities=capabilities,
                skills=skills,
            )
            
            return agent_card
            
        except Exception as e:
            logger.error(f"Error converting agent config to A2A: {e}")
            return None
    
    def _generate_skills_from_config(self, agent_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate A2A skills from agent configuration."""
        skills = []
        
        # Primary skill based on agent topic type
        topic_type = agent_config.get("agent_topic_type", "general assistance")
        if topic_type:
            primary_skill = {
                "id": topic_type.lower().replace(" ", "_"),
                "name": topic_type.title(),
                "description": f"Specialized in {topic_type.lower()}",
                "tags": self._generate_tags_from_topic(topic_type),
                "examples": self._generate_examples_from_topic(topic_type),
                "inputModes": ["text"],
                "outputModes": ["text"],
            }
            
            # Add specific output modes based on topic
            if "image" in topic_type.lower() or "visual" in topic_type.lower():
                primary_skill["outputModes"].append("image")
            elif "video" in topic_type.lower():
                primary_skill["outputModes"].append("video")
            elif "code" in topic_type.lower():
                primary_skill["outputModes"].append("application/json")
                
            skills.append(primary_skill)
        
        # Add general assistance skill
        general_skill = {
            "id": "general_assistance",
            "name": "General Assistance",
            "description": f"{agent_config.get('name', 'Agent')} provides comprehensive AI assistance",
            "tags": ["ai", "assistant", "general", "help"],
            "examples": [
                "How can you help me?",
                "What are your capabilities?",
                "Explain this concept to me",
                "Help me solve this problem"
            ],
            "inputModes": ["text"],
            "outputModes": ["text"],
        }
        skills.append(general_skill)
        
        return skills
    
    def _generate_tags_from_topic(self, topic_type: str) -> List[str]:
        """Generate relevant tags from agent topic type."""
        base_tags = ["ai", "assistant"]
        topic_lower = topic_type.lower()
        
        if "code" in topic_lower or "programming" in topic_lower:
            base_tags.extend(["coding", "programming", "development", "software"])
        elif "image" in topic_lower or "visual" in topic_lower:
            base_tags.extend(["image", "visual", "generation", "creative"])
        elif "data" in topic_lower or "analysis" in topic_lower:
            base_tags.extend(["data", "analysis", "insights", "statistics"])
        elif "video" in topic_lower:
            base_tags.extend(["video", "multimedia", "generation", "creative"])
        elif "text" in topic_lower or "writing" in topic_lower:
            base_tags.extend(["text", "writing", "content", "communication"])
        elif "translation" in topic_lower:
            base_tags.extend(["translation", "language", "multilingual"])
        elif "document" in topic_lower:
            base_tags.extend(["document", "processing", "extraction", "pdf"])
        else:
            base_tags.append("general")
            
        return base_tags
    
    def _generate_examples_from_topic(self, topic_type: str) -> List[str]:
        """Generate example prompts from agent topic type."""
        topic_lower = topic_type.lower()
        
        if "code" in topic_lower or "programming" in topic_lower:
            return [
                "Write a Python function to sort a list",
                "Create a REST API endpoint",
                "Debug this code snippet",
                "Explain this algorithm"
            ]
        elif "image" in topic_lower or "visual" in topic_lower:
            return [
                "Generate an image of a sunset over mountains",
                "Create a logo for my company",
                "Design a user interface mockup",
                "Generate artwork in impressionist style"
            ]
        elif "video" in topic_lower:
            return [
                "Create a promotional video script",
                "Generate video content ideas",
                "Plan a video production workflow",
                "Suggest video editing techniques"
            ]
        elif "data" in topic_lower or "analysis" in topic_lower:
            return [
                "Analyze this dataset for trends",
                "Create a statistical summary",
                "Identify patterns in the data",
                "Generate insights from metrics"
            ]
        elif "document" in topic_lower:
            return [
                "Extract key information from this document",
                "Summarize this PDF",
                "Convert document format",
                "Process and organize document content"
            ]
        else:
            return [
                f"Help me with {topic_type.lower()}",
                f"Explain {topic_type.lower()} concepts",
                f"Assist with {topic_type.lower()} tasks",
                f"Provide guidance on {topic_type.lower()}"
            ]
    
    async def convert_example_agent(self) -> AgentCard:
        """Convert the example agent configuration you provided to A2A format."""
        
        # Your example agent configuration
        example_agent_config = {
            "id": "074e05ee-4be2-4bdd-8738-c911eb8a8a50",
            "name": "ciny",
            "description": "ciny for video generation",
            "avatar": "https://storage.googleapis.com/ruh-dev/agent-avatars/1747662421-Clippathgroup-11.svg",
            "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077",
            "user_ids": ["fce79072-a235-4127-ac5b-b5b1709a8077"],
            "owner_type": "user",
            "template_id": None,
            "template_owner_id": None,
            "is_imported": False,
            "is_bench_employee": False,
            "is_changes_marketplace": False,
            "is_updated": False,
            "is_a2a": False,  # This will be set to True
            "is_customizable": False,
            "agent_category": "ai_agent",
            "system_message": "You are Ciny, an advanced AI assistant specialized in video generation...",
            "model_provider": None,
            "model_name": None,
            "model_api_key": None,
            "workflow_ids": [],
            "mcp_server_ids": ["91ae2f94-0b05-41a2-b23c-da83b465be1b"],
            "agent_topic_type": "video generation",
            "visibility": "private",
            "tags": None,
            "status": "active",
            "department": "IT",
            "organization_id": None,
            "tone": "friendly",
            "files": [],
            "urls": [],
            "ruh_credentials": True,
            "agent_capabilities": {
                "capabilities": None,
                "input_modes": ["text"],
                "output_modes": ["text"],
                "response_model": None,
                "id": "5b1d6c9e-2c00-4e1e-aa06-ae56fec1b71a",
                "created_at": "2025-06-04T10:56:12.691088",
                "updated_at": "2025-06-04T10:56:12.691093"
            },
            "example_prompts": None,
            "category": "marketing",
            "created_at": "2025-06-04T10:56:12.703219",
            "updated_at": "2025-06-04T10:56:12.716397",
            "variables": [],
            "mcps": [],
            "workflows": []
        }
        
        return await self.convert_agent_config_to_a2a(example_agent_config)
    
    async def convert_all_agents(self, dry_run: bool = True) -> List[AgentCard]:
        """
        Convert all agents from the database to A2A format.
        
        Args:
            dry_run: If True, only show what would be converted without making changes
            
        Returns:
            List of converted agent cards
        """
        try:
            # Get all agents from the service
            agents = await self.agent_service.get_all_agents()
            converted_agents = []
            
            logger.info(f"Found {len(agents)} agents to convert")
            
            for agent in agents:
                try:
                    # Convert agent to A2A format
                    # Note: This assumes the agent service returns AgentCard objects
                    # You may need to adapt this based on your actual data structure
                    
                    if dry_run:
                        logger.info(f"Would convert agent: {agent.id} - {agent.name}")
                    else:
                        logger.info(f"Converting agent: {agent.id} - {agent.name}")
                        # Here you would actually update the agent in the database
                        # to set is_a2a=True and update capabilities
                    
                    converted_agents.append(agent)
                    
                except Exception as e:
                    logger.error(f"Error converting agent {agent.id}: {e}")
                    continue
            
            return converted_agents
            
        except Exception as e:
            logger.error(f"Error converting agents: {e}")
            return []
    
    async def close(self):
        """Clean up resources."""
        await self.agent_service.close()


async def main():
    """Main function to run the conversion."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Convert agents to A2A protocol")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be converted without making changes")
    parser.add_argument("--agent-id", help="Convert specific agent by ID")
    parser.add_argument("--example", action="store_true", help="Convert the example agent configuration")
    
    args = parser.parse_args()
    
    converter = AgentA2AConverter()
    
    try:
        if args.example:
            # Convert the example agent
            logger.info("Converting example agent configuration...")
            agent_card = await converter.convert_example_agent()
            if agent_card:
                print("\nConverted Agent Card:")
                print(json.dumps(agent_card.dict(), indent=2))
            else:
                print("Failed to convert example agent")
        
        elif args.agent_id:
            # Convert specific agent
            logger.info(f"Converting agent {args.agent_id}...")
            agent = await converter.agent_service.get_agent(args.agent_id)
            if agent:
                print(f"Found agent: {agent.name}")
                print(json.dumps(agent.dict(), indent=2))
            else:
                print(f"Agent {args.agent_id} not found")
        
        else:
            # Convert all agents
            logger.info("Converting all agents...")
            converted = await converter.convert_all_agents(dry_run=args.dry_run)
            print(f"\nConversion complete. Processed {len(converted)} agents.")
            
            if args.dry_run:
                print("This was a dry run. No changes were made.")
                print("Run without --dry-run to actually convert the agents.")
    
    finally:
        await converter.close()


if __name__ == "__main__":
    asyncio.run(main())
