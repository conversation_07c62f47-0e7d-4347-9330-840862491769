from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum


class DocumentationCategoryEnum(str, Enum):
    """Enum for documentation categories."""
    GUIDES = "guides"
    REFERENCE = "reference"
    TUTORIALS = "tutorials"
    SDKS = "sdks"
    SUPPORT = "support"
    EXAMPLES = "examples"


class DocumentationItem(BaseModel):
    """Schema for a documentation item."""
    id: str = Field(..., description="Unique identifier for the documentation item")
    title: str = Field(..., description="Title of the documentation item")
    category: DocumentationCategoryEnum = Field(..., description="Category of the documentation item")
    summary: str = Field(..., description="Summary of the documentation item")
    updated_at: str = Field(..., description="Last update date of the documentation item")


class DocumentationListResponse(BaseModel):
    """Schema for listing documentation items."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    items: List[DocumentationItem] = Field(..., description="List of documentation items")


class DocumentationContentResponse(BaseModel):
    """Schema for documentation content."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    id: str = Field(..., description="Unique identifier for the documentation item")
    title: str = Field(..., description="Title of the documentation item")
    content: str = Field(..., description="Content of the documentation item in Markdown format")
    category: DocumentationCategoryEnum = Field(..., description="Category of the documentation item")
    updated_at: str = Field(..., description="Last update date of the documentation item")
