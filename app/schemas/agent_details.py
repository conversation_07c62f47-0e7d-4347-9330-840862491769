from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class AgentDetails(BaseModel):
    """Agent details schema for enriching API responses."""

    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    category: Optional[str] = None
    visibility: Optional[str] = None
    status: Optional[str] = None
    tags: List[str] = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    organization_id: Optional[str] = None


class AgentDetailsResponse(BaseModel):
    """Response wrapper for agent details with success status."""

    success: bool
    message: str
    agent: Optional[AgentDetails] = None
