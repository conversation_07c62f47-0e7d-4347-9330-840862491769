"""
SSE (Server-Sent Events) schemas for real-time communication.

This module provides comprehensive Pydantic models for SSE events used in
chat streaming, session management, workflow execution, and MCP operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

from app.shared.constants import ContentType, ConversationType, SSEEventType


class BaseSSEEvent(BaseModel):
    """Base model for all SSE events."""

    event: SSEEventType = Field(
        ...,
        description="Type of SSE event",
        example=SSEEventType.MESSAGE_STREAMING,
    )

    timestamp: Optional[datetime] = Field(
        None, description="Event timestamp", example="2023-12-01T10:30:00Z"
    )


class SessionInitializedEventData(BaseModel):
    """Data structure for session initialization event."""

    session_id: str = Field(
        ...,
        description="Unique session identifier",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(default="initialized", description="Session status", example="initialized")


class SessionInitializedEvent(BaseSSEEvent):
    """Session initialization SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.SESSION_INITIALIZED,
        description="Event type for session initialization",
    )

    data: SessionInitializedEventData = Field(..., description="Session initialization data")


class MessageStreamStartedEventData(BaseModel):
    """Data structure for message stream started event."""

    status: str = Field(default="started", description="Stream status", example="started")


class MessageStreamStartedEvent(BaseSSEEvent):
    """Message stream started SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.MESSAGE_STREAM_STARTED,
        description="Event type for message stream start",
    )

    data: MessageStreamStartedEventData = Field(..., description="Message stream start data")


class MessageStreamingEventData(BaseModel):
    """Data structure for message streaming chunks."""

    message_id: str = Field(
        ...,
        description="Unique message identifier",
        example="msg_123e4567-e89b-12d3-a456-426614174000",
    )

    content: str = Field(
        ...,
        description="Message content chunk",
        example="Hello, this is a streaming response...",
    )

    content_type: ContentType = Field(
        default=ContentType.TEXT_PLAIN,
        description="Content type of the message",
        example=ContentType.TEXT_PLAIN,
    )

    conversation_type: ConversationType = Field(
        default=ConversationType.STANDARD,
        description="Type of conversation",
        example=ConversationType.STANDARD,
    )

    chunk_index: Optional[int] = Field(None, description="Index of the current chunk", example=1)


class MessageStreamingEvent(BaseSSEEvent):
    """Message streaming SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.MESSAGE_STREAMING,
        description="Event type for message streaming",
    )

    data: MessageStreamingEventData = Field(..., description="Message streaming data")


class MessageEndEventData(BaseModel):
    """Data structure for message end event."""

    message_id: str = Field(
        ...,
        description="Unique message identifier",
        example="msg_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(
        default="completed",
        description="Message completion status",
        example="completed",
    )


class MessageEndEvent(BaseSSEEvent):
    """Message end SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.MESSAGE_END,
        description="Event type for message end",
    )

    data: MessageEndEventData = Field(..., description="Message end data")


class MCPExecutionEventData(BaseModel):
    """Data structure for MCP execution events."""

    execution_id: str = Field(
        ...,
        description="Unique MCP execution identifier",
        example="mcp_exec_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(..., description="MCP execution status", example="started")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="MCP execution result (for ended events)",
        example={"output": "http://image.img", "content_type": "image/jpeg"},
    )


class MCPExecutionStartedEvent(BaseSSEEvent):
    """MCP execution started SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.MCP_EXECUTION_STARTED,
        description="Event type for MCP execution start",
    )

    data: MCPExecutionEventData = Field(..., description="MCP execution start data")


class MCPExecutionEndedEvent(BaseSSEEvent):
    """MCP execution ended SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.MCP_EXECUTION_ENDED,
        description="Event type for MCP execution end",
    )

    data: MCPExecutionEventData = Field(..., description="MCP execution end data")


class WorkflowStep(BaseModel):
    """Workflow step information."""

    step_id: str = Field(..., description="Unique step identifier", example="step_001")

    step_name: str = Field(
        ...,
        description="Human-readable step name",
        example="Data Processing",
    )

    step_status: str = Field(..., description="Current step status", example="pending")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="Step execution result",
        example={"processed_items": 100},
    )


class WorkflowExecutionStartedEventData(BaseModel):
    """Data structure for workflow execution started event."""

    workflow_id: str = Field(
        ...,
        description="Unique workflow identifier",
        example="workflow_123e4567-e89b-12d3-a456-426614174000",
    )

    execution_id: str = Field(
        ...,
        description="Unique execution identifier",
        example="exec_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(
        default="started",
        description="Workflow execution status",
        example="started",
    )

    steps: List[WorkflowStep] = Field(
        default_factory=list, description="List of workflow steps", example=[]
    )


class WorkflowExecutionStartedEvent(BaseSSEEvent):
    """Workflow execution started SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.WORKFLOW_EXECUTION_STARTED,
        description="Event type for workflow execution start",
    )

    data: WorkflowExecutionStartedEventData = Field(
        ..., description="Workflow execution start data"
    )


class WorkflowExecutionStepEventData(BaseModel):
    """Data structure for workflow execution step event."""

    workflow_id: str = Field(
        ...,
        description="Unique workflow identifier",
        example="workflow_123e4567-e89b-12d3-a456-426614174000",
    )

    execution_id: str = Field(
        ...,
        description="Unique execution identifier",
        example="exec_123e4567-e89b-12d3-a456-426614174000",
    )

    step: WorkflowStep = Field(..., description="Current workflow step information")


class WorkflowExecutionStepEvent(BaseSSEEvent):
    """Workflow execution step SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.WORKFLOW_EXECUTION_STEP,
        description="Event type for workflow execution step",
    )

    data: WorkflowExecutionStepEventData = Field(..., description="Workflow execution step data")


class ErrorEventData(BaseModel):
    """Data structure for error events."""

    error_code: str = Field(..., description="Error code", example="STREAM_ERROR")

    error_message: str = Field(
        ...,
        description="Human-readable error message",
        example="Failed to process streaming request",
    )

    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error details",
        example={"request_id": "req_123", "retry_after": 30},
    )


class ErrorEvent(BaseSSEEvent):
    """Error SSE event."""

    event: SSEEventType = Field(default=SSEEventType.ERROR, description="Event type for errors")

    data: ErrorEventData = Field(..., description="Error event data")


class TaskDelegationSuccessEventData(BaseModel):
    """Data structure for task delegation success events."""

    task_id: str = Field(
        ...,
        description="Unique task identifier",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )

    session_id: str = Field(
        ...,
        description="Agent session identifier",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )

    agent_session_id: str = Field(
        ...,
        description="Agent session identifier (same as session_id)",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )

    conversation_id: str = Field(
        ...,
        description="Agent conversation identifier",
        example="conv_123e4567-e89b-12d3-a456-426614174000",
    )

    agent_id: str = Field(
        ...,
        description="Agent identifier",
        example="agent_123e4567-e89b-12d3-a456-426614174000",
    )

    global_session_id: Optional[str] = Field(
        None,
        description="Global session identifier",
        example="global_session_123e4567-e89b-12d3-a456-426614174000",
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Correlation identifier for tracking",
        example="corr_123e4567-e89b-12d3-a456-426614174000",
    )

    title: Optional[str] = Field(
        None,
        description="Task title",
        example="Process user request",
    )

    status: str = Field(
        default="success",
        description="Delegation status",
        example="success",
    )


class TaskDelegationSuccessEvent(BaseSSEEvent):
    """Task delegation success SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.TASK_DELEGATION_SUCCESS,
        description="Event type for task delegation success",
    )

    data: TaskDelegationSuccessEventData = Field(..., description="Task delegation success data")


class TaskDelegationFailedEventData(BaseModel):
    """Data structure for task delegation failed events."""

    error_code: str = Field(
        ...,
        description="Error code",
        example="TASK_CREATION_FAILED",
    )

    error_message: str = Field(
        ...,
        description="Error message",
        example="Failed to create task due to validation error",
    )

    correlation_id: Optional[str] = Field(
        None,
        description="Correlation identifier for tracking",
        example="corr_123e4567-e89b-12d3-a456-426614174000",
    )

    global_session_id: Optional[str] = Field(
        None,
        description="Global session identifier",
        example="global_session_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(
        default="failed",
        description="Delegation status",
        example="failed",
    )


class TaskDelegationFailedEvent(BaseSSEEvent):
    """Task delegation failed SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.TASK_DELEGATION_FAILED,
        description="Event type for task delegation failure",
    )

    data: TaskDelegationFailedEventData = Field(..., description="Task delegation failure data")


class KeepAliveEvent(BaseSSEEvent):
    """Keep alive SSE event."""

    event: SSEEventType = Field(
        default=SSEEventType.KEEP_ALIVE,
        description="Event type for keep alive",
    )

    data: str = Field(
        default="keep_alive",
        description="Keep alive data",
        example="keep_alive",
    )


# Union type for all possible SSE events
SSEEvent = Union[
    SessionInitializedEvent,
    MessageStreamStartedEvent,
    MessageStreamingEvent,
    MessageEndEvent,
    MCPExecutionStartedEvent,
    MCPExecutionEndedEvent,
    WorkflowExecutionStartedEvent,
    WorkflowExecutionStepEvent,
    TaskDelegationSuccessEvent,
    TaskDelegationFailedEvent,
    ErrorEvent,
    KeepAliveEvent,
]
