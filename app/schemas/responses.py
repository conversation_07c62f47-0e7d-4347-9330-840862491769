"""
Common response schemas for the Developer API Gateway.

This module provides standardized response models with comprehensive
Swagger documentation, examples, and validation rules.
"""

from typing import Dict, Any, Optional, List, Generic, TypeVar
from pydantic import BaseModel, Field
from enum import Enum

# Generic type for data responses
T = TypeVar("T")


class ResponseStatus(str, Enum):
    """Standard response status values."""

    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"
    PENDING = "pending"


class ErrorType(str, Enum):
    """Types of errors that can occur."""

    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    NOT_FOUND_ERROR = "not_found_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    INTERNAL_ERROR = "internal_error"
    SERVICE_UNAVAILABLE = "service_unavailable"
    TIMEOUT_ERROR = "timeout_error"


class ErrorDetail(BaseModel):
    """Detailed error information."""

    code: str = Field(
        ..., description="Error code for programmatic handling", example="INVALID_QUERY_LENGTH"
    )

    message: str = Field(
        ...,
        description="Human-readable error message",
        example="Query must be between 1 and 10000 characters",
    )

    field: Optional[str] = Field(
        None,
        description="Field name that caused the error (for validation errors)",
        example="query",
    )

    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error context and debugging information",
        example={"current_length": 15000, "max_length": 10000},
    )


class BaseResponse(BaseModel):
    """Base response model with standard fields."""

    success: bool = Field(..., description="Whether the operation was successful", example=True)

    message: str = Field(
        ...,
        description="Human-readable response message",
        example="Operation completed successfully",
    )

    request_id: Optional[str] = Field(
        None,
        description="Unique identifier for request tracing",
        example="req_123e4567-e89b-12d3-a456-426614174000",
    )

    timestamp: Optional[str] = Field(
        None, description="ISO 8601 timestamp of the response", example="2023-12-01T10:30:00Z"
    )


class ErrorResponse(BaseResponse):
    """Standard error response model."""

    success: bool = Field(False, description="Always false for error responses")

    error_type: ErrorType = Field(..., description="Type of error that occurred")

    error_code: str = Field(
        ..., description="Specific error code for programmatic handling", example="AGENT_NOT_FOUND"
    )

    errors: Optional[List[ErrorDetail]] = Field(
        None, description="Detailed error information for validation errors"
    )

    retry_after: Optional[int] = Field(
        None, description="Seconds to wait before retrying (for rate limit errors)", example=60
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "success": False,
                "message": "Agent not found",
                "error_type": "not_found_error",
                "error_code": "AGENT_NOT_FOUND",
                "request_id": "req_123e4567-e89b-12d3-a456-426614174000",
                "timestamp": "2023-12-01T10:30:00Z",
                "errors": [
                    {
                        "code": "AGENT_NOT_FOUND",
                        "message": "The specified agent does not exist",
                        "field": "agent_id",
                        "details": {
                            "agent_id": "agent_123",
                            "available_agents": ["agent_456", "agent_789"],
                        },
                    }
                ],
            }
        }


class DataResponse(BaseResponse, Generic[T]):
    """Generic data response model."""

    data: T = Field(..., description="Response data payload")


class PaginationMeta(BaseModel):
    """Pagination metadata."""

    page: int = Field(..., description="Current page number (1-based)", ge=1, example=1)

    page_size: int = Field(..., description="Number of items per page", ge=1, le=100, example=20)

    total_items: int = Field(..., description="Total number of items available", ge=0, example=150)

    total_pages: int = Field(..., description="Total number of pages available", ge=0, example=8)

    has_next: bool = Field(..., description="Whether there are more pages available", example=True)

    has_previous: bool = Field(
        ..., description="Whether there are previous pages available", example=False
    )


class PaginatedResponse(BaseResponse, Generic[T]):
    """Paginated response model."""

    data: List[T] = Field(..., description="List of items for the current page")

    pagination: PaginationMeta = Field(..., description="Pagination metadata")


class HealthCheckResponse(BaseResponse):
    """Health check response model."""

    service: str = Field(..., description="Service name", example="developer-api-gateway")

    version: str = Field(..., description="Service version", example="1.0.0")

    environment: str = Field(..., description="Environment name", example="production")

    uptime_seconds: Optional[int] = Field(
        None, description="Service uptime in seconds", example=3600
    )

    dependencies: Optional[Dict[str, str]] = Field(
        None,
        description="Status of service dependencies",
        example={"database": "healthy", "kafka": "healthy", "redis": "degraded"},
    )


class AsyncTaskResponse(BaseResponse):
    """Response for asynchronous task operations."""

    task_id: str = Field(
        ...,
        description="Unique identifier for the async task",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(..., description="Current status of the task", example="pending")

    estimated_completion_time: Optional[str] = Field(
        None, description="Estimated completion time (ISO 8601)", example="2023-12-01T10:35:00Z"
    )

    progress_url: Optional[str] = Field(
        None,
        description="URL to check task progress",
        example="/api/v1/tasks/task_123e4567-e89b-12d3-a456-426614174000/status",
    )


class ValidationErrorResponse(ErrorResponse):
    """Specific response for validation errors."""

    error_type: ErrorType = Field(
        ErrorType.VALIDATION_ERROR, description="Always validation_error for this response type"
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "success": False,
                "message": "Validation failed",
                "error_type": "validation_error",
                "error_code": "VALIDATION_FAILED",
                "request_id": "req_123e4567-e89b-12d3-a456-426614174000",
                "timestamp": "2023-12-01T10:30:00Z",
                "errors": [
                    {
                        "code": "FIELD_REQUIRED",
                        "message": "This field is required",
                        "field": "query",
                        "details": {"field_type": "string"},
                    },
                    {
                        "code": "VALUE_TOO_LONG",
                        "message": "Value exceeds maximum length",
                        "field": "message",
                        "details": {"current_length": 15000, "max_length": 10000},
                    },
                ],
            }
        }
