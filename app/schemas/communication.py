from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from app.schemas.agent_details import AgentDetails


# Enum for chat types
class ChatType(str, Enum):
    CHAT_TYPE_UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED"
    CHAT_TYPE_AGENT = "CHAT_TYPE_AGENT"
    CHAT_TYPE_GLOBAL = "CHAT_TYPE_GLOBAL"


# Enum for sender types
class SenderType(str, Enum):
    SENDER_TYPE_UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED"
    SENDER_TYPE_USER = "SENDER_TYPE_USER"
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Enum for message status
class MessageStatus(str, Enum):
    MESSAGE_STATUS_UNSPECIFIED = "MESSAGE_STATUS_UNSPECIFIED"
    MESSAGE_STATUS_RUNNING = "MESSAGE_STATUS_RUNNING"
    MESSAGE_STATUS_COMPLETED = "MESSAGE_STATUS_COMPLETED"
    MESSAGE_STATUS_FAILED = "MESSAGE_STATUS_FAILED"


# Enum for message type
class MessageType(str, Enum):
    MESSAGE_TYPE_UNSPECIFIED = "MESSAGE_TYPE_UNSPECIFIED"
    MESSAGE_TYPE_CHAT = "MESSAGE_TYPE_CHAT"
    MESSAGE_TYPE_MCP = "MESSAGE_TYPE_MCP"
    MESSAGE_TYPE_WORKFLOW = "MESSAGE_TYPE_WORKFLOW"
    MESSAGE_TYPE_USER_MESSAGE = "MESSAGE_TYPE_USER_MESSAGE"


# Enum for task statuses
class TaskStatus(str, Enum):
    TASK_STATUS_UNSPECIFIED = "TASK_STATUS_UNSPECIFIED"
    TASK_STATUS_RUNNING = "TASK_STATUS_RUNNING"
    TASK_STATUS_COMPLETED = "TASK_STATUS_COMPLETED"
    TASK_STATUS_FAILED = "TASK_STATUS_FAILED"
    TASK_STATUS_PAUSED = "TASK_STATUS_PAUSED"
    TASK_STATUS_CANCELLED = "TASK_STATUS_CANCELLED"


# Conversation base model
class ConversationBase(BaseModel):
    chatType: ChatType


# Conversation create model
class ConversationCreate(ConversationBase):
    agentId: Optional[str] = None


# Conversation response model
class ConversationResponse(ConversationBase):
    id: str
    userId: str
    agentId: Optional[str] = None
    inputTokens: int = 0
    outputTokens: int = 0
    createdAt: datetime
    updatedAt: datetime
    title: Optional[str] = None
    tasks: List["TaskResponse"] = []
    agentDetails: Optional[AgentDetails] = None


# Update conversation tokens request model
class UpdateConversationTokensRequest(BaseModel):
    conversationId: str
    inputTokens: Optional[int] = None
    outputTokens: Optional[int] = None


# Pagination metadata
class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


# List of conversations
class ConversationList(BaseModel):
    data: List[ConversationResponse]
    metadata: PaginationMetadata


# Message base model
class MessageBase(BaseModel):
    conversationId: str
    senderType: SenderType
    data: Optional[Dict[str, Any]] = None
    workflowId: Optional[str] = None
    workflowResponse: Optional[List[Dict[str, Any]]] = None
    status: Optional[MessageStatus] = None
    type: MessageType = MessageType.MESSAGE_TYPE_CHAT


# Message create model
class MessageCreate(MessageBase):
    pass


# Direct workflow execution request model
class DirectWorkflowExecutionRequest(BaseModel):
    sessionId: str
    workflowId: str
    correlationId: Optional[str] = None
    message: str
    conversationId: str


# Message response model
class MessageResponse(MessageBase):
    id: str
    createdAt: datetime
    updatedAt: datetime


# Message list model
class MessageList(BaseModel):
    data: List[MessageResponse]
    metadata: PaginationMetadata


# Update message workflow response request model
class UpdateMessageWorkflowResponseRequest(BaseModel):
    newWorkflowResponse: Dict[str, Any]


# Update message status request model
class UpdateMessageStatusRequest(BaseModel):
    status: MessageStatus


# Task base model
class TaskBase(BaseModel):
    title: str
    globalChatConversationId: str
    agentConversationId: str
    agentId: str
    correlationId: Optional[str] = None
    taskStatus: TaskStatus
    sessionId: Optional[str] = None


# Task create model
class TaskCreate(TaskBase):
    pass


# Task response model
class TaskResponse(TaskBase):
    id: str
    createdAt: datetime
    updatedAt: datetime
    agentDetails: Optional[AgentDetails] = None


# Task list model
class TaskList(BaseModel):
    data: List[TaskResponse]
    metadata: PaginationMetadata


# Task delegation request model
class TaskDelegationRequest(BaseModel):
    title: str
    globalChatConversationId: str
    agentId: str
    correlationId: Optional[str] = None
    workflowId: Optional[List[str]] = None
    mcpToolId: Optional[List[str]] = None
    globalSessionId: Optional[str] = None


# Task delegation response model
class TaskDelegationResponse(BaseModel):
    success: bool
    message: str
    taskId: Optional[str] = None
    sessionId: Optional[str] = None
    agentSessionId: Optional[str] = None
    conversationId: Optional[str] = None
    correlationId: Optional[str] = None
    globalSessionId: Optional[str] = None


# Update task status request model
class UpdateTaskStatusRequest(BaseModel):
    taskId: str
    taskStatus: TaskStatus


# Resolve forward references
ConversationResponse.model_rebuild()
