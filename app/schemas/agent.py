"""
Agent execution schemas for the developer API gateway.

This module provides comprehensive Pydantic models for agent execution requests and responses
with detailed Swagger documentation, examples, and validation rules.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, model_validator, validator

from ..shared.enums.chat_enums import Chat<PERSON><PERSON>, Resources


class MessageAttachment(BaseModel):
    """Schema for message attachments (images, documents, etc.)"""

    file_name: str = Field(..., description="Name of the attached file")
    file_type: str = Field(
        ..., description="MIME type of the file (e.g., 'image/jpeg', 'application/pdf')"
    )
    file_size: int = Field(..., description="Size of the file in bytes")
    file_data: Optional[str] = Field(
        None,
        description="Base64 encoded file content (use either file_data or file_url)",
    )
    file_url: Optional[str] = Field(
        None,
        description="URL to the file if stored externally (use either file_data or file_url)",
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata about the file"
    )

    @model_validator(mode="before")
    @classmethod
    def validate_file_data_or_url(cls, values):
        """Validate that either file_data or file_url is provided, but not both"""
        file_data = values.get("file_data")
        file_url = values.get("file_url")

        # Check if neither is provided
        if not file_data and not file_url:
            raise ValueError("Either file_data or file_url must be provided")

        # Check if both are provided (both have non-empty values)
        if file_data and file_url:
            raise ValueError("Provide either file_data or file_url, not both")

        return values


class AgentExecutionStatus(str, Enum):
    """Enumeration of possible agent execution statuses."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """Enumeration of supported task types."""

    QUERY = "query"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    PROCESSING = "processing"
    CUSTOM = "custom"


class AgentExecutionRequest(BaseModel):
    """
    Request model for executing an agent task.

    This model defines the structure for requesting agent task execution
    with comprehensive validation and documentation.
    """

    query: str = Field(
        ...,
        description="The user's query or task to be processed by the agent",
        min_length=1,
        max_length=10000,
        example="What is the capital of France and what are its main attractions?",
    )

    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Optional metadata for the execution request including priority, timeout, and custom parameters",
        example={
            "priority": "high",
            "timeout": 120,
            "context": "educational",
            "format": "detailed",
        },
    )

    @validator("query")
    def validate_query(cls, v):
        """Validate query content."""
        if not v.strip():
            raise ValueError("Query cannot be empty or only whitespace")
        return v.strip()

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "query": "Analyze the current market trends for renewable energy stocks",
                "metadata": {
                    "priority": "high",
                    "timeout": 300,
                    "context": "financial_analysis",
                    "include_charts": True,
                    "data_sources": ["yahoo_finance", "bloomberg"],
                },
            }
        }


class AgentQueryRequest(BaseModel):
    """Request model for querying an agent directly."""

    query: str = Field(
        ...,
        description="The user's query to be processed by the agent",
        min_length=1,
        max_length=10000,
        example="What is the capital of France?",
    )

    user_id: Optional[str] = Field(
        None, description="ID of the user making the query", example="user_123"
    )

    organization_id: Optional[str] = Field(
        None, description="ID of the organization", example="org_456"
    )

    variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Variables to pass to the agent",
        example={"context": "business", "priority": "high"},
    )


class AgentQueryResponse(BaseModel):
    """Response model for agent query."""

    success: bool = Field(..., description="Whether the query was successful")

    message: str = Field(..., description="Status message")

    agent_response: Optional[Dict[str, Any]] = Field(
        None, description="The agent's response content"
    )

    run_id: str = Field(..., description="Unique identifier for this query execution")

    final: bool = Field(True, description="Whether this is the final response")


class ChatContext(BaseModel):
    """Chat context message."""

    role: str = Field(..., description="Role of the message sender", example="user")

    content: str = Field(
        ..., description="Content of the message", example="Hello, how can you help me?"
    )


class EnhancedChatRequest(BaseModel):
    """Enhanced request model for sending a chat message."""

    chat_context: List[ChatContext] = Field(
        ..., description="List of chat messages in the conversation context"
    )


class EnhancedChatResponse(BaseModel):
    """Enhanced response model for a chat message."""

    success: bool = Field(..., description="Whether the chat was successful")

    message: str = Field(..., description="Status message")

    agent_response: Optional[Dict[str, Any]] = Field(
        None, description="The agent's response content"
    )

    run_id: str = Field(..., description="Unique identifier for this chat execution")

    final: bool = Field(True, description="Whether this is the final response")


class AgentExecutionResponse(BaseModel):
    """Response model for agent execution."""

    task_id: str = Field(..., description="Unique identifier for the execution task")

    session_id: str = Field(..., description="Session identifier for the agent interaction")

    status: str = Field(..., description="Current status of the task", example="submitted")

    agent_id: str = Field(..., description="ID of the agent that processed the task")

    result: Optional[Dict[str, Any]] = Field(
        None, description="Task execution result (available when status is 'completed')"
    )

    error: Optional[str] = Field(None, description="Error message if the task failed")

    execution_time_ms: Optional[int] = Field(None, description="Execution time in milliseconds")


class AgentTaskStatus(BaseModel):
    """Model for checking agent execution status."""

    task_id: str = Field(..., description="Task identifier")

    status: str = Field(..., description="Current status of the task", example="running")

    progress: Optional[float] = Field(
        None, description="Task progress percentage (0-100)", ge=0, le=100
    )

    estimated_completion_time: Optional[int] = Field(
        None, description="Estimated completion time in seconds"
    )


class AgentExecutionError(BaseModel):
    """Model for agent execution errors."""

    error_code: str = Field(..., description="Error code identifier")

    error_message: str = Field(..., description="Human-readable error message")

    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class CreateSessionRequest(BaseModel):
    """
    Request model for creating a new agent session.

    A session represents a conversation context with an agent and is required
    for sending messages and executing tasks. If a conversation_id is provided,
    the session will be initialized with the existing conversation history.
    """

    conversation_id: str = Field(
        ...,
        description="conversation ID to initialize session with existing conversation history",
        min_length=1,
        max_length=100,
        example="conv_123e4567-e89b-12d3-a456-************",
    )

    agent_id: Optional[str] = Field(
        None,
        description="ID of the agent to use for the session (required if not using orchestration team)",
        min_length=1,
        max_length=100,
        example="agent_123e4567-e89b-12d3-a456-************",
    )

    use_orchestration_team: bool = Field(
        False,
        description="Whether to use the orchestration team instead of a single agent",
        example=False,
    )

    use_knowledge: Optional[bool] = Field(
        default=False,
        description="use organization knowledge",
        example="true",
    )

    @model_validator(mode="before")
    @classmethod
    def check_agent_id_required(cls, values):
        use_orchestration_team = values.get("use_orchestration_team", False)
        agent_id = values.get("agent_id")
        if not use_orchestration_team and not agent_id:
            raise ValueError("agent_id is required when use_orchestration_team is False")
        return values


class CreateSessionResponse(BaseModel):
    """
    Response model for successful session creation.

    Contains the unique session identifier that should be used for subsequent
    chat messages and task executions.
    """

    session_id: str = Field(
        ...,
        description="Unique identifier of the created session",
        example="session_123e4567-e89b-12d3-a456-************",
    )


class ChatMessageRequest(BaseModel):
    """
    Request model for sending a chat message to an agent.

    The message will be processed by the agent within the context of the session.
    """

    message: str = Field(
        ...,
        description="The message content to send to the agent",
        min_length=1,
        max_length=10000,
        example="Hello! Can you help me analyze this data?",
    )

    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )

    mode: ChatMode = Field(
        default=ChatMode.ASK,
        description="Mode of the chat message: 'ASK' for general queries, 'TASK' for task execution",
        example="ASK",
    )

    tools: Optional[List[str]] = Field(default_factory=list, description="list mcp of tools ids")

    resource: Resources = Field(
        default=Resources.ALL,
        description="resources to search the data",
        example="ALL",
    )


class ChatMessageResponse(BaseModel):
    """
    Response model for agent chat message.

    Contains the agent's response and optionally a task ID if the agent
    created a background task to process the request.
    """

    message: str = Field(
        ...,
        description="The agent's response message",
        example="I'd be happy to help you analyze your data. Please share the data you'd like me to review.",
    )

    task_id: Optional[str] = Field(
        None,
        description="ID of any background task created by the agent",
        example="task_123e4567-e89b-12d3-a456-************",
    )


class QuickChatMessageResponse(BaseModel):
    """
    Response model for quick chat message response.

    Returns immediately with a processing ID while the actual message
    processing happens in the background.
    """

    success: bool = Field(
        True,
        description="Whether the message was successfully queued for processing",
        example=True,
    )

    message: str = Field(
        ...,
        description="Status message indicating the message was sent",
        example="Message sent successfully and is being processed",
    )

    processing_id: str = Field(
        ...,
        description="Unique identifier to track the message processing status",
        example="proc_123e4567-e89b-12d3-a456-************",
    )

    session_id: str = Field(
        ...,
        description="The session ID for this chat message",
        example="session_123e4567-e89b-12d3-a456-************",
    )


class MessageProcessingStatus(BaseModel):
    """
    Response model for message processing status.

    Contains the current status of a message being processed in the background.
    """

    processing_id: str = Field(
        ...,
        description="The processing ID to track",
        example="proc_123e4567-e89b-12d3-a456-************",
    )

    status: str = Field(
        ...,
        description="Current processing status",
        example="completed",
        # Possible values: "processing", "completed", "failed"
    )

    message: Optional[str] = Field(
        None,
        description="The agent's response message (available when status is completed)",
        example="I'd be happy to help you analyze your data.",
    )

    task_id: Optional[str] = Field(
        None,
        description="ID of any background task created by the agent",
        example="task_123e4567-e89b-12d3-a456-************",
    )

    error: Optional[str] = Field(
        None,
        description="Error message if processing failed",
        example="Failed to process message: timeout",
    )

    created_at: Optional[str] = Field(
        None,
        description="When the processing started",
        example="2024-01-15T10:30:00Z",
    )

    completed_at: Optional[str] = Field(
        None,
        description="When the processing completed",
        example="2024-01-15T10:30:05Z",
    )


class ExecuteTaskRequest(BaseModel):
    """
    Request model for executing a specific task with an agent.

    Tasks are structured operations that agents can perform with specific
    inputs and expected outputs.
    """

    task_type: str = Field(
        ...,
        description="Type of task to execute",
        min_length=1,
        max_length=100,
        example="data_analysis",
    )

    task_input: Dict[str, Any] = Field(
        ...,
        description="Input parameters for the task",
        example={"data": [1, 2, 3, 4, 5], "analysis_type": "statistical_summary", "format": "json"},
    )


class ExecuteTaskResponse(BaseModel):
    """
    Response model for task execution.

    Contains the task identifier, current status, and results if completed.
    """

    task_id: str = Field(
        ...,
        description="Unique identifier of the executed task",
        example="task_123e4567-e89b-12d3-a456-************",
    )

    status: str = Field(..., description="Current status of the task", example="completed")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="Task execution result (available when status is 'completed')",
        example={"summary": {"mean": 3.0, "median": 3.0, "std": 1.58}, "analysis_complete": True},
    )


class RunAgentTaskRequest(BaseModel):
    """
    Request model for running a task directly with an agent.

    This is a simplified interface that doesn't require session management.
    """

    agent_id: str = Field(
        ...,
        description="Unique identifier of the agent to run the task",
        min_length=1,
        max_length=100,
        example="agent_123e4567-e89b-12d3-a456-************",
    )

    task_type: str = Field(
        ...,
        description="Type of task to execute",
        min_length=1,
        max_length=100,
        example="text_generation",
    )

    task_input: Dict[str, Any] = Field(
        ...,
        description="Input parameters for the task",
        example={
            "prompt": "Write a summary of renewable energy benefits",
            "max_length": 500,
            "style": "professional",
        },
    )


class RunAgentTaskResponse(BaseModel):
    """
    Response model for direct task execution.

    Contains the task results and execution metadata.
    """

    task_id: str = Field(
        ...,
        description="Unique identifier of the executed task",
        example="task_123e4567-e89b-12d3-a456-************",
    )

    status: str = Field(..., description="Final status of the task execution", example="completed")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="Task execution result and metadata",
        example={
            "generated_text": "Renewable energy offers numerous benefits...",
            "word_count": 487,
            "execution_time_ms": 1250,
        },
    )


class StreamingChatMessageRequest(BaseModel):
    """
    Request model for sending a streaming chat message to an agent.

    This model is used for chat messages that expect streaming responses,
    allowing real-time interaction with agents.
    """

    message: str = Field(
        ...,
        description="The message content to send to the agent for streaming response",
        min_length=1,
        max_length=10000,
        example="Can you explain machine learning concepts step by step?",
    )

    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )


# Orchestration Team Models
class OrchestrationTeamSessionRequest(BaseModel):
    """
    Request model for creating an orchestration team session.

    An orchestration team session enables multi-agent collaboration with
    human-in-the-loop capabilities for complex problem solving.
    """

    conversation_id: Optional[str] = Field(
        None,
        description="Optional conversation ID to initialize session with existing history",
        min_length=1,
        max_length=100,
        example="conv_123e4567-e89b-12d3-a456-************",
    )

    variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Variables to pass to the orchestration team",
        example={"domain": "software_development", "complexity": "high"},
    )


class OrchestrationTeamSessionResponse(BaseModel):
    """
    Response model for orchestration team session creation.
    """

    session_id: str = Field(
        ...,
        description="Unique identifier for the orchestration team session",
        example="orch_session_123e4567-e89b-12d3-a456-************",
    )

    message: str = Field(
        ...,
        description="Status message for session creation",
        example="Orchestration team session created successfully",
    )

    success: bool = Field(
        ...,
        description="Whether the session was created successfully",
        example=True,
    )


class OrchestrationTeamChatRequest(BaseModel):
    """
    Request model for sending a chat message to the orchestration team.

    The message will be processed by multiple agents working together,
    with optional human input for complex decisions.
    """

    message: str = Field(
        ...,
        description="The message content to send to the orchestration team",
        min_length=1,
        max_length=10000,
        example="I need help designing a scalable microservices architecture for an e-commerce platform.",
    )

    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )

    mode: ChatMode = Field(
        default=ChatMode.ASK,
        description="Mode of the chat message: 'ASK' for general queries, 'TASK' for task execution",
        example="ASK",
    )


class OrchestrationTeamChatResponse(BaseModel):
    """
    Response model for orchestration team chat message.

    Contains the team's collaborative response and metadata about the
    decision-making process.
    """

    message: str = Field(
        ...,
        description="Status message for the chat request",
        example="Message sent to orchestration team successfully",
    )

    success: bool = Field(
        ...,
        description="Whether the message was processed successfully",
        example=True,
    )

    run_id: str = Field(
        ...,
        description="Unique identifier for this chat execution",
        example="run_123e4567-e89b-12d3-a456-************",
    )

    team_response: Optional[Dict[str, Any]] = Field(
        None,
        description="The orchestration team's collaborative response",
        example={
            "content": "Based on our analysis, we recommend a microservices architecture with...",
            "participating_agents": ["architect", "security_expert", "performance_analyst"],
            "confidence_score": 0.95,
            "requires_human_input": False,
        },
    )

    final: bool = Field(
        False,
        description="Whether this is the final response for this conversation",
        example=False,
    )


class HumanInputRequest(BaseModel):
    """
    Request model for human input during orchestration team processing.

    This is sent when the team needs human guidance or decision-making.
    """

    prompt: str = Field(
        ...,
        description="The prompt or question for human input",
        example="The team has identified two architectural approaches. Which would you prefer?",
    )

    context: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Recent conversation context for the human input request",
        example=[
            {"role": "user", "content": "Design a microservices architecture"},
            {"role": "architect", "content": "I suggest approach A with API Gateway"},
            {"role": "security_expert", "content": "I recommend approach B with service mesh"},
        ],
    )

    team_conversation_id: str = Field(
        ...,
        description="Unique identifier for the team conversation requiring input",
        example="team_conv_123e4567-e89b-12d3-a456-************",
    )

    requesting_agent: str = Field(
        ...,
        description="The agent requesting human input",
        example="orchestration_coordinator",
    )

    options: Optional[List[str]] = Field(
        None,
        description="Optional list of predefined options for the human to choose from",
        example=["Approach A: API Gateway", "Approach B: Service Mesh", "Hybrid Approach"],
    )


class HumanInputResponse(BaseModel):
    """
    Response model for providing human input to the orchestration team.
    """

    team_conversation_id: str = Field(
        ...,
        description="The team conversation ID this input is for",
        example="team_conv_123e4567-e89b-12d3-a456-************",
    )

    user_input: str = Field(
        ...,
        description="The human's input or decision",
        min_length=1,
        max_length=5000,
        example="I prefer Approach A with API Gateway, but please ensure it includes proper security measures.",
    )


class StreamingChatChunk(BaseModel):
    """
    Model for individual chunks in a streaming chat response.

    Each chunk represents a piece of the agent's response as it's being generated.
    """

    chunk_id: Optional[str] = Field(
        None, description="Unique identifier for this chunk", example="chunk_001"
    )

    content: Optional[str] = Field(
        None,
        description="Text content of this chunk",
        example="Machine learning is a subset of artificial intelligence...",
    )

    chunk_type: str = Field(
        default="text", description="Type of content in this chunk", example="text"
    )

    is_final: bool = Field(
        default=False, description="Whether this is the final chunk in the stream"
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata for this chunk",
        example={"timestamp": "2024-01-15T10:30:00Z", "token_count": 15, "confidence": 0.95},
    )


class DeleteSessionRequest(BaseModel):
    """
    Request model for deleting an agent session.

    This model defines the structure for requesting session deletion
    with optional parameters for force deletion and reason tracking.
    """

    reason: Optional[str] = Field(
        "user_request",
        description="Reason for session deletion",
        max_length=200,
        example="chat_complete",
    )

    force: bool = Field(
        False,
        description="Whether to force delete the session even if it doesn't exist",
        example=False,
    )

    @validator("reason")
    def validate_reason(cls, v):
        """Validate reason content."""
        if v and len(v.strip()) == 0:
            raise ValueError("Reason cannot be empty if provided")
        return v.strip() if v else "user_request"

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "reason": "chat_complete",
                "force": False,
            }
        }


class DeleteSessionResponse(BaseModel):
    """
    Response model for successful session deletion.

    Contains the deletion status, session identifier, and metadata about the deletion.
    """

    success: bool = Field(
        ...,
        description="Whether the session was successfully deleted",
        example=True,
    )

    session_id: str = Field(
        ...,
        description="Unique identifier of the deleted session",
        example="session_123e4567-e89b-12d3-a456-************",
    )

    message: str = Field(
        ...,
        description="Status message describing the deletion result",
        example="Session deleted successfully",
    )

    deleted_at: Optional[str] = Field(
        None,
        description="ISO timestamp when the session was deleted",
        example="2024-01-15T10:30:00Z",
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "success": True,
                "session_id": "session_123e4567-e89b-12d3-a456-************",
                "message": "Session deleted successfully",
                "deleted_at": "2024-01-15T10:30:00Z",
            }
        }
