from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class AgentCapability(str, Enum):
    """Enum for agent capabilities."""

    TEXT_CHAT = "text_chat"
    VOICE_CHAT = "voice_chat"
    IMAGE_GENERATION = "image_generation"
    CODE_GENERATION = "code_generation"
    DATA_ANALYSIS = "data_analysis"
    DOCUMENT_PROCESSING = "document_processing"
    TRANSLATION = "translation"
    SUMMARIZATION = "summarization"
    QUESTION_ANSWERING = "question_answering"
    CUSTOM = "custom"


class AgentStatus(str, Enum):
    """Enum for agent status."""

    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    MAINTENANCE = "maintenance"


class MessageType(str, Enum):
    """Enum for message types."""

    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    FILE = "file"
    STRUCTURED_DATA = "structured_data"
    ACTION = "action"
    SYSTEM = "system"


class AgentCard(BaseModel):
    """Schema for agent card (metadata about an agent)."""

    id: str = Field(..., description="Unique identifier for the agent")
    name: str = Field(..., description="Name of the agent")
    description: str = Field(..., description="Description of the agent")
    capabilities: List[AgentCapability] = Field(..., description="List of agent capabilities")
    status: AgentStatus = Field(..., description="Current status of the agent")
    version: str = Field(..., description="Version of the agent")
    created_at: datetime = Field(..., description="When the agent was created")
    updated_at: datetime = Field(..., description="When the agent was last updated")
    avatar_url: Optional[str] = Field(None, description="URL to the agent's avatar image")
    provider: Optional[str] = Field(None, description="Provider of the agent")
    endpoint_url: str = Field(..., description="URL endpoint for communicating with the agent")
    custom_capabilities: Optional[List[str]] = Field(
        None, description="List of custom capabilities not in the standard enum"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata about the agent"
    )


class AgentMessage(BaseModel):
    """Schema for messages exchanged between agents."""

    id: str = Field(..., description="Unique identifier for the message")
    sender_id: str = Field(..., description="ID of the sending agent")
    recipient_id: str = Field(..., description="ID of the receiving agent")
    conversation_id: str = Field(..., description="ID of the conversation")
    message_type: MessageType = Field(..., description="Type of message")
    content: Union[str, Dict[str, Any]] = Field(..., description="Message content")
    timestamp: datetime = Field(..., description="When the message was sent")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata about the message"
    )
    in_reply_to: Optional[str] = Field(None, description="ID of the message this is replying to")


class AgentConversation(BaseModel):
    """Schema for a conversation between agents."""

    id: str = Field(..., description="Unique identifier for the conversation")
    participants: List[str] = Field(
        ..., description="List of agent IDs participating in the conversation"
    )
    created_at: datetime = Field(..., description="When the conversation was created")
    updated_at: datetime = Field(..., description="When the conversation was last updated")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata about the conversation"
    )
    status: str = Field(..., description="Status of the conversation")


class CreateConversationRequest(BaseModel):
    """Schema for creating a new conversation."""

    participants: List[str] = Field(
        ..., description="List of agent IDs to include in the conversation"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata about the conversation"
    )


class CreateConversationResponse(BaseModel):
    """Schema for the response when creating a new conversation."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    conversation: Optional[AgentConversation] = Field(None, description="The created conversation")


class SendMessageRequest(BaseModel):
    """Schema for sending a message."""

    conversation_id: str = Field(..., description="ID of the conversation")
    message_type: MessageType = Field(..., description="Type of message")
    content: Union[str, Dict[str, Any]] = Field(..., description="Message content")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata about the message"
    )
    in_reply_to: Optional[str] = Field(None, description="ID of the message this is replying to")


class SendMessageResponse(BaseModel):
    """Schema for the response when sending a message."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    message_id: Optional[str] = Field(None, description="ID of the sent message")


class GetAgentCardRequest(BaseModel):
    """Schema for requesting an agent card."""

    agent_id: str = Field(..., description="ID of the agent")


class GetAgentCardResponse(BaseModel):
    """Schema for the response when requesting an agent card."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    agent_card: Optional[AgentCard] = Field(None, description="The agent card")


class ListAgentCardsResponse(BaseModel):
    """Schema for the response when listing agent cards."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    agent_cards: List[AgentCard] = Field(..., description="List of agent cards")


class GetConversationRequest(BaseModel):
    """Schema for requesting a conversation."""

    conversation_id: str = Field(..., description="ID of the conversation")


class GetConversationResponse(BaseModel):
    """Schema for the response when requesting a conversation."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    conversation: Optional[AgentConversation] = Field(None, description="The conversation")


class ListMessagesRequest(BaseModel):
    """Schema for listing messages in a conversation."""

    conversation_id: str = Field(..., description="ID of the conversation")
    page: int = Field(1, description="Page number")
    page_size: int = Field(20, description="Number of messages per page")


class ListMessagesResponse(BaseModel):
    """Schema for the response when listing messages."""

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    messages: List[AgentMessage] = Field(..., description="List of messages")
    total: int = Field(..., description="Total number of messages")
    page: int = Field(..., description="Current page number")
    total_pages: int = Field(..., description="Total number of pages")


class JSONRPCErrorCode(int, Enum):
    """JSON-RPC 2.0 error codes."""

    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603
    SERVER_ERROR_START = -32000
    SERVER_ERROR_END = -32099


class A2AErrorCode(int, Enum):
    """A2A-specific error codes (within JSON-RPC server error range)."""

    TASK_NOT_FOUND = -32001
    TASK_NOT_CANCELABLE = -32002
    PUSH_NOTIFICATION_NOT_SUPPORTED = -32003
    UNSUPPORTED_OPERATION = -32004
    CONTENT_TYPE_NOT_SUPPORTED = -32005
    INVALID_AGENT_RESPONSE = -32006


class JSONRPCError(BaseModel):
    """JSON-RPC 2.0 error object."""

    code: int = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    data: Optional[Any] = Field(None, description="Additional error data")


class JSONRPCResponse(BaseModel):
    """JSON-RPC 2.0 response object."""

    jsonrpc: Literal["2.0"] = Field("2.0", description="JSON-RPC version")
    id: Optional[str] = Field(None, description="Request ID")
    result: Optional[Any] = Field(None, description="Result of the request")
    error: Optional[JSONRPCError] = Field(None, description="Error object")

    @field_validator("result", "error")
    def check_result_or_error(cls, v, info):
        """Validate that either result or error is present, but not both."""
        values = info.data
        if "result" in values and "error" in values:
            if values["result"] is not None and values["error"] is not None:
                raise ValueError("Cannot have both result and error")
        return v


class JSONParseError(JSONRPCError):
    """JSON parse error."""

    code: Literal[JSONRPCErrorCode.PARSE_ERROR] = Field(JSONRPCErrorCode.PARSE_ERROR)
    message: Literal["Parse error"] = Field("Parse error")


class InvalidRequestError(JSONRPCError):
    """Invalid request error."""

    code: Literal[JSONRPCErrorCode.INVALID_REQUEST] = Field(JSONRPCErrorCode.INVALID_REQUEST)
    message: Literal["Invalid request"] = Field("Invalid request")


class MethodNotFoundError(JSONRPCError):
    """Method not found error."""

    code: Literal[JSONRPCErrorCode.METHOD_NOT_FOUND] = Field(JSONRPCErrorCode.METHOD_NOT_FOUND)
    message: Literal["Method not found"] = Field("Method not found")


class InvalidParamsError(JSONRPCError):
    """Invalid params error."""

    code: Literal[JSONRPCErrorCode.INVALID_PARAMS] = Field(JSONRPCErrorCode.INVALID_PARAMS)
    message: Literal["Invalid params"] = Field("Invalid params")


class InternalError(JSONRPCError):
    """Internal error."""

    code: Literal[JSONRPCErrorCode.INTERNAL_ERROR] = Field(JSONRPCErrorCode.INTERNAL_ERROR)
    message: Literal["Internal error"] = Field("Internal error")


class A2ARequest(BaseModel):
    """Base class for A2A protocol requests."""

    jsonrpc: Literal["2.0"] = Field("2.0", description="JSON-RPC version")
    id: Optional[str] = Field(None, description="Request ID")
    method: str = Field(..., description="Method name")
    params: Dict[str, Any] = Field({}, description="Method parameters")

    @classmethod
    def validate_python(cls, obj: Any) -> "A2ARequest":
        """Validate and convert a Python object to an A2ARequest."""
        if not isinstance(obj, dict):
            raise ValueError("Request must be a dictionary")

        if obj.get("jsonrpc") != "2.0":
            raise ValueError("Invalid JSON-RPC version")

        method = obj.get("method")
        if not method or not isinstance(method, str):
            raise ValueError("Invalid method")

        # Determine the request type based on the method (A2A spec methods)
        if method == "message/send":
            return MessageSendRequest(**obj)
        elif method == "message/stream":
            return MessageStreamRequest(**obj)
        elif method == "tasks/get":
            return TasksGetRequest(**obj)
        elif method == "tasks/cancel":
            return TasksCancelRequest(**obj)
        elif method == "tasks/pushNotificationConfig/set":
            return TasksPushNotificationSetRequest(**obj)
        elif method == "tasks/pushNotificationConfig/get":
            return TasksPushNotificationGetRequest(**obj)
        elif method == "tasks/resubscribe":
            return TasksResubscribeRequest(**obj)
        else:
            raise ValueError(f"Unknown method: {method}")


class AgentCard(BaseModel):
    """Agent card containing metadata about an agent."""

    name: str = Field(..., description="Name of the agent")
    description: str = Field(..., description="Description of the agent")
    url: str = Field(..., description="URL of the agent")
    version: str = Field(..., description="Version of the agent")
    defaultInputModes: List[str] = Field(..., description="Default input modes")
    defaultOutputModes: List[str] = Field(..., description="Default output modes")
    capabilities: Dict[str, Any] = Field(..., description="Agent capabilities")
    skills: Optional[List[Dict[str, Any]]] = Field(None, description="Agent skills")


# Message-related schemas
class MessageSendParams(BaseModel):
    """Parameters for message/send method."""

    message: Dict[str, Any] = Field(..., description="Message to send")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Configuration")


class MessageSendRequest(A2ARequest):
    """Request to send a message."""

    method: Literal["message/send"] = Field("message/send")
    params: MessageSendParams = Field(..., description="Message send parameters")


class MessageStreamRequest(A2ARequest):
    """Request to send a streaming message."""

    method: Literal["message/stream"] = Field("message/stream")
    params: MessageSendParams = Field(..., description="Message stream parameters")


# Task-related schemas
class TaskIdParams(BaseModel):
    """Parameters containing task ID."""

    id: str = Field(..., description="Task ID")


class TasksGetRequest(A2ARequest):
    """Request to get a task."""

    method: Literal["tasks/get"] = Field("tasks/get")
    params: TaskIdParams = Field(..., description="Task query parameters")


class TasksCancelRequest(A2ARequest):
    """Request to cancel a task."""

    method: Literal["tasks/cancel"] = Field("tasks/cancel")
    params: TaskIdParams = Field(..., description="Task cancel parameters")


class TasksPushNotificationSetRequest(A2ARequest):
    """Request to set task push notification."""

    method: Literal["tasks/pushNotificationConfig/set"] = Field("tasks/pushNotificationConfig/set")
    params: Dict[str, Any] = Field(..., description="Push notification parameters")


class TasksPushNotificationGetRequest(A2ARequest):
    """Request to get task push notification."""

    method: Literal["tasks/pushNotificationConfig/get"] = Field("tasks/pushNotificationConfig/get")
    params: TaskIdParams = Field(..., description="Task ID parameters")


class TasksResubscribeRequest(A2ARequest):
    """Request to resubscribe to a task."""

    method: Literal["tasks/resubscribe"] = Field("tasks/resubscribe")
    params: TaskIdParams = Field(..., description="Task resubscribe parameters")
