from pydantic import BaseModel, Field, EmailStr
from typing import Optional


class RegisterRequest(BaseModel):
    """Schema for registration request."""
    email: EmailStr = Field(..., description="Email address")
    password: str = Field(..., description="Password", min_length=8)
    full_name: str = Field(..., description="Full name")


class RegisterResponse(BaseModel):
    """Schema for registration response."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    user_id: str = Field(..., description="ID of the registered user")


class LoginResponse(BaseModel):
    """Schema for login response."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(..., description="Token type")
    token_expire_at: float = Field(..., description="Timestamp when the token expires")
    user_id: str = Field(..., description="ID of the authenticated user")


class TokenResponse(BaseModel):
    """Schema for token response."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(..., description="Token type")
    token_expire_at: float = Field(..., description="Timestamp when the token expires")


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request."""
    refresh_token: str = Field(..., description="JWT refresh token")


class RefreshTokenResponse(BaseModel):
    """Schema for refresh token response."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    access_token: str = Field(..., description="New JWT access token")
    token_type: str = Field(..., description="Token type")
    token_expire_at: float = Field(..., description="Timestamp when the token expires")
