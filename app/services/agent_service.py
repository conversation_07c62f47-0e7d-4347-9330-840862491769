from typing import Any, Dict, List, Optional

import httpx

from app.core.config import settings
from app.core.logging import get_logger
from app.schemas.a2a import AgentCard


from app.services.agent_grpc_service import AgentServiceClient as GrpcAgentServiceClient

logger = get_logger(__name__)

GRPC_AVAILABLE = True


class AgentServiceClient:
    """
    Client for interacting with the Agent Service via gRPC.

    This class provides methods for retrieving agent information,
    executing agents, and managing agent state using the main API
    gateway's gRPC service.
    """

    def __init__(self, base_url: str = None):
        """
        Initialize the Agent Service Client.

        Args:
            base_url: Base URL for the Agent Service API (fallback for HTTP)
        """
        self.base_url = base_url or getattr(settings, "AGENT_SERVICE_URL", "http://localhost:8000")
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=30.0)

        # Initialize gRPC client if available and enabled
        self.grpc_client = None
        grpc_enabled = getattr(settings, "AGENT_SERVICE_GRPC_ENABLED", False)

        if GRPC_AVAILABLE and grpc_enabled:
            try:
                # Use the dedicated gRPC service client
                self.grpc_client = GrpcAgentServiceClient()
                logger.info("gRPC agent service client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize gRPC client: {e}")
                logger.info("Falling back to HTTP-only mode")
                self.grpc_client = None
        elif not grpc_enabled:
            logger.info("gRPC client disabled by configuration, using mock data")
        else:
            logger.info("gRPC not available, using mock data")

    def _convert_grpc_agent_to_card(self, grpc_agent) -> AgentCard:
        """Convert gRPC agent response to AgentCard with A2A compatibility."""
        try:
            # Extract capabilities from agent data with A2A support
            capabilities = {
                "text_chat": True,
                "streaming": True,
                "pushNotifications": True,
                "stateTransitionHistory": False,
            }

            # Add specific capabilities based on agent topic type
            if hasattr(grpc_agent, "agent_topic_type") and grpc_agent.agent_topic_type:
                topic_type = grpc_agent.agent_topic_type.lower()
                if "code" in topic_type or "programming" in topic_type:
                    capabilities["code_generation"] = True
                elif "image" in topic_type or "visual" in topic_type:
                    capabilities["image_generation"] = True
                elif "data" in topic_type or "analysis" in topic_type:
                    capabilities["data_analysis"] = True
                elif "document" in topic_type:
                    capabilities["document_processing"] = True
                elif "video" in topic_type:
                    capabilities["video_generation"] = True

            if hasattr(grpc_agent, "agent_capabilities"):
                capabilities["question_answering"] = True

            # Generate skills based on agent configuration
            skills = self._generate_skills_from_agent_config(grpc_agent)

            # Get base URL from environment for A2A protocol
            import os

            base_url = os.getenv("A2A_BASE_URL", "http://localhost:8000")
            agent_url = f"{base_url}/api/v1/a2a/agents/{grpc_agent.id}/a2a"

            return AgentCard(
                id=grpc_agent.id,
                name=grpc_agent.name,
                description=grpc_agent.description or f"{grpc_agent.name} - AI Assistant",
                url=agent_url,
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities=capabilities,
                skills=skills,
            )
        except Exception as e:
            logger.error(f"Error converting gRPC agent to card: {e}")
            return None

    def _generate_skills_from_agent_config(self, grpc_agent) -> List[Dict[str, Any]]:
        """
        Generate A2A skills from agent configuration.

        Args:
            grpc_agent: The gRPC agent object

        Returns:
            List of skill dictionaries
        """
        skills = []

        # Primary skill based on agent topic type
        topic_type = getattr(grpc_agent, "agent_topic_type", "general assistance")
        if topic_type:
            primary_skill = {
                "id": topic_type.lower().replace(" ", "_"),
                "name": topic_type.title(),
                "description": f"Specialized in {topic_type.lower()}",
                "tags": self._generate_tags_from_topic(topic_type),
                "examples": self._generate_examples_from_topic(topic_type),
                "inputModes": ["text"],
                "outputModes": ["text"],
            }

            # Add specific output modes based on topic
            if "image" in topic_type.lower() or "visual" in topic_type.lower():
                primary_skill["outputModes"].append("image")
            elif "code" in topic_type.lower():
                primary_skill["outputModes"].append("application/json")

            skills.append(primary_skill)

        # Add general assistance skill
        general_skill = {
            "id": "general_assistance",
            "name": "General Assistance",
            "description": f"{grpc_agent.name} provides comprehensive AI assistance",
            "tags": ["ai", "assistant", "general", "help"],
            "examples": [
                "How can you help me?",
                "What are your capabilities?",
                "Explain this concept to me",
                "Help me solve this problem",
            ],
            "inputModes": ["text"],
            "outputModes": ["text"],
        }
        skills.append(general_skill)

        return skills

    def _generate_tags_from_topic(self, topic_type: str) -> List[str]:
        """Generate relevant tags from agent topic type."""
        base_tags = ["ai", "assistant"]
        topic_lower = topic_type.lower()

        if "code" in topic_lower or "programming" in topic_lower:
            base_tags.extend(["coding", "programming", "development", "software"])
        elif "image" in topic_lower or "visual" in topic_lower:
            base_tags.extend(["image", "visual", "generation", "creative"])
        elif "data" in topic_lower or "analysis" in topic_lower:
            base_tags.extend(["data", "analysis", "insights", "statistics"])
        elif "video" in topic_lower:
            base_tags.extend(["video", "multimedia", "generation", "creative"])
        elif "text" in topic_lower or "writing" in topic_lower:
            base_tags.extend(["text", "writing", "content", "communication"])
        elif "translation" in topic_lower:
            base_tags.extend(["translation", "language", "multilingual"])
        elif "document" in topic_lower:
            base_tags.extend(["document", "processing", "extraction", "pdf"])
        else:
            base_tags.append("general")

        return base_tags

    def _generate_examples_from_topic(self, topic_type: str) -> List[str]:
        """Generate example prompts from agent topic type."""
        topic_lower = topic_type.lower()

        if "code" in topic_lower or "programming" in topic_lower:
            return [
                "Write a Python function to sort a list",
                "Create a REST API endpoint",
                "Debug this code snippet",
                "Explain this algorithm",
            ]
        elif "image" in topic_lower or "visual" in topic_lower:
            return [
                "Generate an image of a sunset over mountains",
                "Create a logo for my company",
                "Design a user interface mockup",
                "Generate artwork in impressionist style",
            ]
        elif "video" in topic_lower:
            return [
                "Create a promotional video script",
                "Generate video content ideas",
                "Plan a video production workflow",
                "Suggest video editing techniques",
            ]
        elif "data" in topic_lower or "analysis" in topic_lower:
            return [
                "Analyze this dataset for trends",
                "Create a statistical summary",
                "Identify patterns in the data",
                "Generate insights from metrics",
            ]
        elif "document" in topic_lower:
            return [
                "Extract key information from this document",
                "Summarize this PDF",
                "Convert document format",
                "Process and organize document content",
            ]
        else:
            return [
                f"Help me with {topic_type.lower()}",
                f"Explain {topic_type.lower()} concepts",
                f"Assist with {topic_type.lower()} tasks",
                f"Provide guidance on {topic_type.lower()}",
            ]

    async def get_all_agents(self) -> List[AgentCard]:
        """
        Get all available agents.

        Returns:
            List of agent cards
        """
        try:
            # Try gRPC first if available
            if self.grpc_client and GRPC_AVAILABLE:
                try:
                    # Use the gRPC client to list agents
                    response = await self.grpc_client.list_agents(
                        page=1,
                        page_size=100,  # Get a large number of agents
                        is_a2a=True,  # Only get A2A-enabled agents
                    )

                    agent_cards = []
                    for grpc_agent in response.agents:
                        card = self._convert_grpc_agent_to_card(grpc_agent)
                        if card:
                            agent_cards.append(card)

                    logger.info(f"Retrieved {len(agent_cards)} agents via gRPC")
                    return agent_cards

                except Exception as e:
                    logger.error(f"Error getting agents via gRPC: {e}")
                    # Fall back to mock data

            # Fallback to mock data
            logger.info("Using mock agent data")
            return list(self.mock_agents.values())

        except Exception as e:
            logger.error(f"Error getting agents: {e}")
            return list(self.mock_agents.values())

    async def get_agent(self, agent_id: str) -> Optional[AgentCard]:
        """
        Get an agent by ID.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent card or None if not found
        """
        try:
            # Try gRPC first if available
            if self.grpc_client and GRPC_AVAILABLE:
                try:
                    # Use the gRPC client to get agent by ID
                    response = await self.grpc_client.getAgentById(agent_id)

                    if response.success and response.agent:
                        card = self._convert_grpc_agent_to_card(response.agent)
                        if card:
                            logger.info(f"Retrieved agent {agent_id} via gRPC")
                            return card

                except Exception as e:
                    logger.error(f"Error getting agent {agent_id} via gRPC: {e}")
                    # Fall back to mock data

            # Fallback to mock data
            return self.mock_agents.get(agent_id)

        except Exception as e:
            logger.error(f"Error getting agent {agent_id}: {e}")
            return self.mock_agents.get(agent_id)

    async def execute_agent(self, agent_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an agent with the given input.

        Note: This method is deprecated for A2A protocol usage.
        A2A protocol uses KafkaAgentExecutor directly for agent execution.

        Args:
            agent_id: ID of the agent to execute
            input_data: Input data for the agent

        Returns:
            Agent execution result

        Raises:
            Exception: If the agent execution fails
        """
        try:
            # This is a fallback implementation for non-A2A usage
            # A2A protocol uses KafkaAgentExecutor directly
            logger.warning(f"Using fallback agent execution for {agent_id}")
            return {
                "success": True,
                "message": "Agent executed via fallback method",
                "result": {
                    "output": f"Fallback response from agent {agent_id}",
                    "metadata": {
                        "execution_time": 0.1,
                        "method": "fallback",
                    },
                },
            }
        except Exception as e:
            logger.error(f"Error executing agent {agent_id}: {e}")
            raise

    async def get_a2a_agents(self) -> List[AgentCard]:
        """
        Get all A2A-enabled agents specifically.

        Returns:
            List of A2A-compatible agent cards
        """
        try:
            # Try gRPC first if available
            if self.grpc_client and GRPC_AVAILABLE:
                try:
                    # Use the gRPC client to list A2A agents
                    response = await self.grpc_client.list_agents(
                        page=1,
                        page_size=100,
                        is_a2a=True,  # Only get A2A-enabled agents
                    )

                    agent_cards = []
                    for grpc_agent in response.agents:
                        # Only include agents that have is_a2a=True
                        if hasattr(grpc_agent, "is_a2a") and grpc_agent.is_a2a:
                            card = self._convert_grpc_agent_to_card(grpc_agent)
                            if card:
                                agent_cards.append(card)

                    logger.info(f"Retrieved {len(agent_cards)} A2A agents via gRPC")
                    return agent_cards

                except Exception as e:
                    logger.error(f"Error getting A2A agents via gRPC: {e}")
                    # Fall back to mock data

            # Fallback to mock data (all mock agents are A2A-compatible)
            logger.info("Using mock A2A agent data")
            return list(self.mock_agents.values())

        except Exception as e:
            logger.error(f"Error getting A2A agents: {e}")
            return list(self.mock_agents.values())

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
