"""
A2A Agent Integration for the API gateway.

This module provides integration between the A2A protocol and the agent service.
It allows agents to be exposed via the A2A protocol.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Awaitable, Callable, Dict, List, Optional

try:
    # Try to import the actual A2A SDK
    from a2a.server.agent_execution import AgentExecutor, RequestContext
    from a2a.server.events import EventQueue
    from a2a.types import Message, Part, Task, TextPart, UnsupportedOperationError
    from a2a.utils import completed_task, new_agent_text_message

    A2A_SDK_AVAILABLE = True
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("A2A SDK not available. Using mock implementation.")
    A2A_SDK_AVAILABLE = False

    # Mock implementations if A2A SDK is not available
    class RequestContext:
        def __init__(self, context_id: str, user_input: str):
            self.context_id = context_id
            self.user_input = user_input

        def get_user_input(self) -> str:
            return self.user_input

    class EventQueue:
        async def push(self, event: Dict[str, Any]):
            pass

    class AgentExecutor:
        async def execute(self, context: RequestContext, event_queue: EventQueue):
            raise NotImplementedError()

        async def cancel(self, context: RequestContext, event_queue: EventQueue):
            raise NotImplementedError()

    class UnsupportedOperationError(Exception):
        pass

    def completed_task(task_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        return {"id": task_id, "status": {"state": "completed"}, "result": result}

    def new_agent_text_message(content: str) -> Dict[str, Any]:
        return {"message": {"content": content, "parts": [{"type": "text", "text": content}]}}


from app.schemas.a2a import AgentCapability, AgentCard, AgentStatus
from app.services.kafka_service import kafka_service

logger = logging.getLogger(__name__)


class KafkaAgentExecutor(AgentExecutor):
    """
    AgentExecutor implementation for Kafka-based agents.

    This class bridges between the A2A protocol and Kafka-based agents.
    """

    def __init__(self, agent_id: str):
        """
        Initialize the KafkaAgentExecutor.

        Args:
            agent_id: The ID of the agent
        """
        self.agent_id = agent_id

    async def execute(self, context: RequestContext, event_queue: EventQueue):
        """
        Execute the agent with the given context.

        Args:
            context: The request context
            event_queue: The event queue for streaming responses

        Returns:
            The execution result
        """
        try:
            # Get the user input from the context
            user_input = context.get_user_input()

            # Create a session for the agent
            session_id = await kafka_service.create_agent_session(self.agent_id, "a2a_user")

            # Send the message to the agent
            response = await kafka_service.send_chat_message(session_id, user_input)

            # Extract the response from the result
            agent_response = response.get("agent_response", {})
            content = agent_response.get("content", "")

            # Create a completed task with the response
            task = completed_task(
                task_id=context.context_id,
                result=new_agent_text_message(content=content),
            )

            return task
        except Exception as e:
            logger.error(f"Error executing agent {self.agent_id}: {e}")

            # Send failure status update if A2A SDK is available
            if A2A_SDK_AVAILABLE:
                try:
                    await event_queue.push(
                        {
                            "type": "task_status_update",
                            "task_id": context.context_id,
                            "status": {
                                "state": "failed",
                                "message": f"Task failed: {str(e)}",
                                "timestamp": self._get_current_timestamp(),
                            },
                        }
                    )
                except Exception as eq_error:
                    logger.error(f"Error pushing to event queue: {eq_error}")

            # Return a completed task with the error
            task = completed_task(
                task_id=context.context_id,
                result=new_agent_text_message(content=f"Error: {str(e)}"),
            )
            return task

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime

        return datetime.utcnow().isoformat() + "Z"

    async def cancel(self, context: RequestContext, event_queue: EventQueue):
        """
        Cancel the agent execution.

        Args:
            context: The request context
            event_queue: The event queue for streaming responses

        Raises:
            UnsupportedOperationError: This operation is not supported yet
        """
        # Cancel is not supported yet
        raise UnsupportedOperationError()


class KafkaAgentRegistry:
    """
    Registry for Kafka-based agents.

    This class manages the registration of Kafka-based agents with the A2A server.
    """

    def __init__(self, a2a_server: Any):
        """
        Initialize the KafkaAgentRegistry.

        Args:
            a2a_server: The A2A server instance
        """
        self.a2a_server = a2a_server
        self.registered_agents: Dict[str, str] = {}

    def register_agent(
        self, agent_id: str, name: str, description: str, capabilities: List[AgentCapability] = None
    ):
        """
        Register a Kafka-based agent with the A2A server.

        Args:
            agent_id: The ID of the agent
            name: The name of the agent
            description: The description of the agent
            capabilities: The capabilities of the agent
        """
        # Create an agent card for the Kafka-based agent
        agent_card = AgentCard(
            id=agent_id,
            name=name,
            description=description,
            capabilities=capabilities or [AgentCapability.TEXT_CHAT],
            status=AgentStatus.ONLINE,
            version="1.0.0",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            avatar_url=f"https://example.com/avatars/{agent_id}.png",
            provider="Kafka",
            endpoint_url=f"https://api.example.com/agents/{agent_id}",
            metadata={
                "description": description,
            },
        )

        # Register the agent with the A2A server
        self.a2a_server.register_agent(agent_card)

        # Store the agent ID
        self.registered_agents[agent_id] = agent_id

        logger.info(f"Registered Kafka-based agent {agent_id} with A2A server")

    def get_agent(self, agent_id: str) -> Optional[str]:
        """
        Get a Kafka-based agent by ID.

        Args:
            agent_id: The ID of the agent

        Returns:
            The agent ID or None if not found
        """
        return self.registered_agents.get(agent_id)

    def list_agents(self) -> List[str]:
        """
        List all registered Kafka-based agents.

        Returns:
            List of agent IDs
        """
        return list(self.registered_agents.keys())
