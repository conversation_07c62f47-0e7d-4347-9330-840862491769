"""
Kafka service for communicating with agents.

This module provides a service for sending and receiving messages to/from
agents via Kafka.
"""

import asyncio
import json
import threading
import uuid
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from aiokafka import AI<PERSON>afkaConsumer, AIOKafkaProducer  # type: ignore
from pydantic import BaseModel

from app.core.config import settings
from app.core.logging import get_logger

# Route to SSE manager for streaming
from app.helper.sse_manager import sse_manager
from app.services.communication_service import CommunicationServiceClient
from app.services.event_handler import EventHandler
from app.shared import constants as communication_schemas
from app.shared.constants import MessageStatus, MessageType, SenderType
from app.utils.redis.redis_service import RedisService

from ..shared.constants import SSEEventType
from ..shared.enums.chat_enums import ChatMode, ResponseMode
from ..shared.enums.employee_enum import Global_Employee

# Configure logging
logger = get_logger(__name__)


# Kafka topics - centralized configuration
@dataclass(frozen=True)
class KafkaTopics:
    """Centralized Kafka topic configuration."""

    AGENT_CREATION = settings.KAFKA_AGENT_CREATION_TOPIC
    AGENT_CHAT = settings.KAFKA_AGENT_CHAT_TOPIC
    AGENT_RESPONSE = settings.KAFKA_AGENT_RESPONSE_TOPIC
    AGENT_QUERY = settings.KAFKA_AGENT_QUERY_TOPIC
    AGENT_SESSION_DELETION = settings.KAFKA_AGENT_SESSION_DELETION_TOPIC
    AGENT_CHAT_STOP = settings.KAFKA_AGENT_CHAT_STOP_TOPIC
    ORCHESTRATION_TEAM_SESSION = settings.KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC
    ORCHESTRATION_TEAM_CHAT = settings.KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC
    HUMAN_INPUT_RESPONSE = settings.KAFKA_HUMAN_INPUT_RESPONSE_TOPIC
    HUMAN_INPUT_REQUEST = settings.KAFKA_HUMAN_INPUT_REQUEST_TOPIC
    WORKFLOW_REPLY = settings.WORKFLOW_REPLY_TOPIC


# Constants for timeouts and configuration
class TimeoutConfig:
    """Centralized timeout configuration."""

    DEFAULT = 30
    SESSION_CREATION = 90  # Increased for complex operations
    CHAT_MESSAGE = 600
    TASK_EXECUTION = 180  # Increased for complex tasks
    ORCHESTRATION_TEAM = 180  # Increased for team operations


# Concurrency configuration
class ConcurrencyConfig:
    """Centralized concurrency configuration."""

    MAX_CONCURRENT_SESSIONS = 50  # Max concurrent session creations
    MAX_CONCURRENT_MESSAGES = 100  # Max concurrent message processing
    MESSAGE_PROCESSING_WORKERS = 10  # Background message processors
    CONSUMER_BATCH_SIZE = 10  # Messages to process in batch


class CorrelationIdPrefix:
    """Correlation ID prefixes for different operation types."""

    SESSION_CREATE = "session_create"
    ORCH_SESSION_CREATE = "orch_session_create"
    CHAT = "chat"
    TASK = "task"


# Response completion criteria
@dataclass
class ResponseCriteria:
    """Criteria for determining when a response should complete a future."""

    is_message_response: bool = False
    is_session_init: bool = False
    is_error: bool = False
    is_text_from_agent: bool = False
    is_final_message: bool = False

    @property
    def should_complete(self) -> bool:
        """Check if any completion criteria is met."""
        return (
            self.is_message_response
            or self.is_error
            or self.is_session_init
            or self.is_text_from_agent
            or self.is_final_message
        )


def serialize_for_kafka(obj: Any) -> Any:
    """
    Recursively serialize objects for Kafka JSON serialization.

    Converts Pydantic models to dictionaries and handles nested structures.
    """
    if isinstance(obj, BaseModel):
        # Use model_dump for Pydantic v2 compatibility with JSON serialization
        if hasattr(obj, "model_dump"):
            return obj.model_dump(mode="json")
        else:
            # Fallback for Pydantic v1
            return obj.dict()
    elif isinstance(obj, dict):
        return {key: serialize_for_kafka(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_kafka(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(serialize_for_kafka(item) for item in obj)
    else:
        return obj


class KafkaService:
    """
    Service for communicating with agents via Kafka.

    This service provides thread-safe operations for Kafka communication
    with optimized performance and comprehensive error handling.
    """

    def __init__(self):
        """Initialize the Kafka service."""
        self.producer = None
        self.consumer = None
        self._initialized = False
        self.bootstrap_servers = settings.KAFKA_BOOTSTRAP_SERVERS

        # Thread-safe response handler management
        self.response_handlers: Dict[str, asyncio.Future] = {}
        self._response_lock = threading.RLock()

        self.consumer_task = None
        self.sse_manager = sse_manager
        self.redis_service = RedisService()
        self.event_handler = EventHandler(self.sse_manager)

        # Cache for communication service to avoid repeated instantiation
        self._communication_service = None

        # Topics configuration
        self.topics = KafkaTopics()

        # Concurrency control
        self._session_semaphore = asyncio.Semaphore(ConcurrencyConfig.MAX_CONCURRENT_SESSIONS)
        self._message_semaphore = asyncio.Semaphore(ConcurrencyConfig.MAX_CONCURRENT_MESSAGES)
        self._message_queue = asyncio.Queue(maxsize=1000)
        self._worker_tasks: List[asyncio.Task] = []

    @property
    def communication_service(self) -> CommunicationServiceClient:
        """Lazy-loaded communication service instance."""
        if self._communication_service is None:
            self._communication_service = CommunicationServiceClient()
        return self._communication_service

    def _generate_correlation_id(self, prefix: str, identifier: Optional[str] = None) -> str:
        """
        Generate unique correlation ID with operation-specific prefix.

        Args:
            prefix: Operation type prefix
            identifier: Optional identifier (session_id, agent_id, etc.)

        Returns:
            Unique correlation ID
        """
        unique_id = str(uuid.uuid4())
        if identifier:
            return f"{prefix}_{identifier}_{unique_id}"
        return f"{prefix}_{unique_id}"

    def _add_response_handler(self, correlation_id: str, future: asyncio.Future) -> None:
        """Thread-safe addition of response handler."""
        with self._response_lock:
            if correlation_id in self.response_handlers:
                logger.warning(
                    f"Correlation ID {correlation_id} already exists, " "replacing existing handler"
                )
            self.response_handlers[correlation_id] = future

    def _remove_response_handler(self, correlation_id: str) -> None:
        """Thread-safe removal of response handler."""
        with self._response_lock:
            self.response_handlers.pop(correlation_id, None)

    def _get_response_handler(self, correlation_id: str) -> Optional[asyncio.Future]:
        """Thread-safe retrieval of response handler."""
        with self._response_lock:
            return self.response_handlers.get(correlation_id)

    async def initialize(self):
        """Initialize the Kafka producer and consumer."""
        if self._initialized:
            return

        try:
            # Initialize producer
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                max_request_size=524288000,  # 500MB max request size
            )

            # Initialize consumer
            self.consumer = AIOKafkaConsumer(
                self.topics.AGENT_RESPONSE,
                self.topics.HUMAN_INPUT_REQUEST,
                self.topics.WORKFLOW_REPLY,
                bootstrap_servers=self.bootstrap_servers,
                group_id="agent-api-gateway",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )

            # Start producer and consumer
            await self.producer.start()
            await self.consumer.start()

            # Start consumer task
            self.consumer_task = asyncio.create_task(self._consume_messages())

            # Start background message processing workers
            for i in range(ConcurrencyConfig.MESSAGE_PROCESSING_WORKERS):
                worker_task = asyncio.create_task(self._message_processing_worker(f"worker-{i}"))
                self._worker_tasks.append(worker_task)

            self._initialized = True
            logger.info(
                f"Kafka service initialized successfully with "
                f"{ConcurrencyConfig.MESSAGE_PROCESSING_WORKERS} workers"
            )
        except Exception as e:
            logger.error(f"Failed to initialize Kafka service: {e}")
            await self.cleanup()
            raise

    async def cleanup(self):
        """Clean up Kafka resources."""
        if self.consumer_task:
            self.consumer_task.cancel()
            try:
                await self.consumer_task
            except asyncio.CancelledError:
                pass

        # Cancel all worker tasks
        for worker_task in self._worker_tasks:
            worker_task.cancel()

        # Wait for all workers to finish
        if self._worker_tasks:
            try:
                await asyncio.gather(*self._worker_tasks, return_exceptions=True)
            except Exception:
                pass
        self._worker_tasks.clear()

        if self.producer:
            await self.producer.stop()

        if self.consumer:
            await self.consumer.stop()

        # Clean up pending response handlers
        with self._response_lock:
            for correlation_id, future in self.response_handlers.items():
                if not future.done():
                    future.cancel()
            self.response_handlers.clear()

        self._initialized = False
        logger.info("Kafka service cleaned up")

    def _extract_headers(self, msg_headers) -> Dict[str, str]:
        """Extract and decode message headers."""
        if not msg_headers:
            return {}

        return {
            k.decode("utf-8") if isinstance(k, bytes) else k: (
                v.decode("utf-8") if isinstance(v, bytes) else v
            )
            for k, v in msg_headers
        }

    def _evaluate_response_criteria(self, response: Dict[str, Any]) -> ResponseCriteria:
        """Evaluate if response meets completion criteria."""
        event_type = response.get("event_type")
        agent_response = response.get("agent_response", {})

        return ResponseCriteria(
            is_message_response=(event_type == SSEEventType.MESSAGE_RESPONSE.value),
            is_session_init=(event_type == SSEEventType.SESSION_INITIALIZED.value),
            is_error=(event_type == SSEEventType.ERROR.value),
            is_text_from_agent=(
                agent_response.get("message_type") == "text"
                and agent_response.get("source") != "user"
            ),
            is_final_message=response.get("final", False),
        )

    def _should_store_message(self, event_type: str, agent_response: Dict[str, Any]) -> bool:
        """Determine if message should be stored."""
        return (
            event_type == SSEEventType.MESSAGE_RESPONSE.value
            or (
                agent_response.get("message_type") == "text"
                and agent_response.get("source") != "user"
            )
            or (
                agent_response.get("message_type") == "structured_message"
                and agent_response.get("source") != "user"
            )
        )

    def _should_send_message_end(self, message_type: str) -> bool:
        """Determine if message end should be sent."""
        return message_type in ["task_result", "user_input_request"]

    async def send_message(
        self,
        topic: str,
        message: Dict[str, Any],
        correlation_id: str = None,
        reply_topic: str = None,
    ) -> str:
        """
        Send a message to a Kafka topic.

        Args:
            topic: The Kafka topic to send the message to
            message: The message to send
            correlation_id: The correlation ID for the message (generated if
                not provided)
            reply_topic: The topic to receive replies on

        Returns:
            The correlation ID for the message
        """
        if not self._initialized:
            await self.initialize()

        # Generate correlation ID if not provided
        if not correlation_id:
            correlation_id = str(uuid.uuid4())

        # Add correlation ID to message
        message["run_id"] = correlation_id

        # Prepare headers
        headers = [
            ("correlationId", correlation_id.encode("utf-8")),
            ("reply-topic", (reply_topic or self.topics.AGENT_RESPONSE).encode("utf-8")),
        ]

        try:
            # Serialize message for Kafka (handles Pydantic models)
            serialized_message = serialize_for_kafka(message)

            # Encode and send message
            value = json.dumps(serialized_message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic} with correlation ID " f"{correlation_id}")
            return correlation_id
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def _consume_messages(self):
        """Consume messages from Kafka and route them to handlers."""
        try:
            async for msg in self.consumer:
                try:
                    # Extract correlation ID from headers
                    headers_dict = self._extract_headers(msg.headers)
                    correlation_id = headers_dict.get("correlationId")

                    # Decode message
                    response = json.loads(msg.value.decode("utf-8"))
                    topic = msg.topic

                    # If no correlation ID in headers, try to extract from response
                    if not correlation_id:
                        correlation_id = response.get("run_id") or response.get("correlation_id")
                        if correlation_id:
                            logger.info(
                                f"Extracted correlation ID from response data: {correlation_id}"
                            )

                    logger.info(
                        f"Received message with topic {topic} and "
                        f"correlation ID {correlation_id}"
                    )

                    # Add message to processing queue for async handling
                    message_data = {
                        "topic": topic,
                        "correlation_id": correlation_id,
                        "response": response,
                        "headers": headers_dict,
                    }

                    try:
                        # Non-blocking put with timeout
                        await asyncio.wait_for(self._message_queue.put(message_data), timeout=1.0)
                    except asyncio.TimeoutError:
                        logger.warning(
                            f"Message queue full, processing message directly: " f"{correlation_id}"
                        )
                        # Process directly if queue is full
                        await self._process_message_data(message_data)

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode message: {e}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")

        except asyncio.CancelledError:
            logger.info("Consumer task cancelled")
        except Exception as e:
            logger.error(f"Error in consumer task: {e}")

    async def _message_processing_worker(self, worker_name: str):
        """Background worker for processing messages from the queue."""
        logger.info(f"Message processing worker {worker_name} started")

        try:
            while True:
                try:
                    # Get message from queue with timeout
                    message_data = await asyncio.wait_for(self._message_queue.get(), timeout=5.0)

                    # Process the message
                    await self._process_message_data(message_data)

                    # Mark task as done
                    self._message_queue.task_done()

                except asyncio.TimeoutError:
                    # Continue loop on timeout (normal behavior)
                    continue
                except Exception as e:
                    logger.error(f"Error in message processing worker {worker_name}: {e}")
                    # Mark task as done even on error to prevent queue blocking
                    try:
                        self._message_queue.task_done()
                    except ValueError:
                        pass  # task_done() called more times than put()

        except asyncio.CancelledError:
            logger.info(f"Message processing worker {worker_name} cancelled")
        except Exception as e:
            logger.error(f"Fatal error in message processing worker {worker_name}: {e}")

    async def _process_message_data(self, message_data: Dict[str, Any]):
        """Process a single message data item."""
        topic = message_data["topic"]
        correlation_id = message_data["correlation_id"]
        response = message_data["response"]

        try:
            # Handle event using the centralized event handler
            await self.event_handler.handle_event(topic, correlation_id, response)

            # Route to regular handler (backward compatibility)
            await self._handle_response_routing(correlation_id, response)

            # Handle message storage and completion
            await self._handle_message_processing(response, topic, correlation_id)

        except Exception as e:
            logger.error(f"Error processing message data for correlation {correlation_id}: {e}")

    async def _handle_response_routing(self, correlation_id: str, response: Dict[str, Any]) -> None:
        """Handle routing of responses to waiting futures."""
        if not correlation_id:
            logger.warning("Received response with no correlation ID")
            return

        future = self._get_response_handler(correlation_id)
        if not future:
            return

        if future.done():
            logger.warning(f"Handler for correlation ID {correlation_id} already completed")
            return

        # Evaluate completion criteria
        criteria = self._evaluate_response_criteria(response)

        logger.info(
            f"Response criteria for {correlation_id}: "
            f"should_complete={criteria.should_complete}, "
            f"is_session_init={criteria.is_session_init}, "
            f"event_type={response.get('event_type')}, "
            f"session_id={response.get('session_id')}, "
            f"communication_type={response.get('communication_type')}"
        )

        if criteria.should_complete:
            try:
                future.set_result(response)
                logger.info(
                    f"Successfully routed response to handler for correlation ID {correlation_id}"
                )
            except asyncio.InvalidStateError:
                logger.warning(f"Future for correlation ID {correlation_id} already completed")

    async def _handle_message_processing(
        self, response: Dict[str, Any], topic: str, correlation_id: str
    ) -> None:
        """Handle message storage and completion logic."""
        event_type = response.get("event_type")
        agent_response = response.get("agent_response", {})
        message_type = agent_response.get("message_type", "")

        # Store chat messages if criteria met
        if self._should_store_message(event_type, agent_response):
            await self._store_chat_messages(
                session_id=response.get("session_id"),
                agent_response=agent_response,
            )

        # Send message end if needed
        if self._should_send_message_end(message_type):
            session_id = response.get("session_id")
            self.sse_manager.send_message_end(
                session_id=session_id,
                client_id=session_id,
            )
            await self.update_task(session_id=session_id)

        # Handle workflow responses
        if topic == self.topics.WORKFLOW_REPLY:
            await self._store_chat_messages(
                correlation_id=correlation_id,
                workflow_response=response,
            )

    async def wait_for_response(
        self, correlation_id: str, timeout: int = TimeoutConfig.DEFAULT
    ) -> Dict[str, Any]:
        """
        Wait for a response with the given correlation ID.

        Args:
            correlation_id: The correlation ID to wait for
            timeout: The timeout in seconds

        Returns:
            The response message
        """
        if not self._initialized:
            await self.initialize()

        # Check if correlation ID already exists and clean it up
        existing_future = self._get_response_handler(correlation_id)
        if existing_future:
            logger.warning(
                f"Correlation ID {correlation_id} already has a pending handler, "
                "cleaning up existing handler"
            )
            if not existing_future.done():
                existing_future.cancel()
            self._remove_response_handler(correlation_id)

        # Create future for response
        future = asyncio.Future()

        logger.info(f"Adding response handler for correlation ID {correlation_id}")
        self._add_response_handler(correlation_id, future)

        try:
            # Wait for response with timeout
            logger.info(
                f"Waiting for response with correlation ID {correlation_id}, timeout: {timeout}s"
            )
            response = await asyncio.wait_for(future, timeout=timeout)
            logger.info(f"Received response for correlation ID {correlation_id}")
            return response
        except asyncio.TimeoutError:
            logger.error(
                f"Timeout waiting for response with correlation ID {correlation_id}. "
                f"Timeout was {timeout} seconds."
            )
            raise
        except asyncio.CancelledError:
            logger.info(f"Wait cancelled for correlation ID {correlation_id}")
            raise
        finally:
            # Clean up handler
            self._remove_response_handler(correlation_id)

    async def _handle_error_response(
        self, response: Dict[str, Any], operation: str, **context
    ) -> None:
        """Handle error responses consistently."""
        event_type = response.get("event_type")
        if event_type == SSEEventType.ERROR.value:
            error_message = response.get("message", "Unknown error occurred")
            error_details = response.get("error", {})
            logger.error(
                f"Error in {operation}: {error_message}",
                extra={"error_details": error_details, **context},
            )
            raise ValueError(f"Failed to {operation}: {error_message}")

    async def create_agent_session(
        self,
        agent_id: str,
        user_id: str,
        organization_id: str = None,
        use_knowledge: bool = False,
        conversation_messages: List[Dict[str, str]] = None,
    ) -> str:
        """Create a new agent session with optional conversation context."""
        # Use semaphore to limit concurrent session creations
        async with self._session_semaphore:
            # Generate unique correlation ID
            correlation_id = self._generate_correlation_id(
                CorrelationIdPrefix.SESSION_CREATE, agent_id
            )

            # Prepare creation request
            creation_request = {
                "agent_id": agent_id,
                "user_id": user_id,
                "organization_id": organization_id,
                "communication_type": "single",
                "use_knowledge": use_knowledge,
                "variables": {},
            }

            # Add conversation context if provided
            if conversation_messages:
                creation_request["conversation_context"] = conversation_messages

            logger.info(
                f"Creating agent session for agent {agent_id} with "
                f"correlation ID {correlation_id}"
            )

            # Send request and wait for response
            await self.send_message(self.topics.AGENT_CREATION, creation_request, correlation_id)

            response = await self.wait_for_response(
                correlation_id, timeout=TimeoutConfig.SESSION_CREATION
            )

            # Handle error response
            await self._handle_error_response(
                response, "create agent session", agent_id=agent_id, correlation_id=correlation_id
            )

            # Extract session ID
            session_id = response.get("session_id")
            if not session_id:
                raise ValueError("No session ID in response")

            logger.info(f"Agent session created with ID {session_id}")
            return session_id

    async def send_chat_message(
        self, session_id: str, message: str, attachments: Optional[List] = None
    ) -> Dict[str, Any]:
        """Send a chat message to an agent."""
        # Use semaphore to limit concurrent message processing
        async with self._message_semaphore:
            # Generate unique correlation ID
            correlation_id = self._generate_correlation_id(CorrelationIdPrefix.CHAT, session_id)

            chat_request = {
                "session_id": session_id,
                "chat_context": [{"role": "user", "content": message}],
                "chat_response": "stream",
                "attachments": attachments,
            }

            logger.info(
                f"Sending chat message for session {session_id} with "
                f"correlation ID {correlation_id}"
            )

            # Send request and start streaming
            await self.send_message(
                self.topics.AGENT_CHAT, chat_request, correlation_id=correlation_id
            )

            self.sse_manager.send_message_stream_started(client_id=session_id)

            # Wait for response
            response = await self.wait_for_response(
                correlation_id, timeout=TimeoutConfig.CHAT_MESSAGE
            )

            # Handle error response
            await self._handle_error_response(
                response, "send chat message", correlation_id=correlation_id, session_id=session_id
            )

            return response

    async def stop_agent_chat_stream(self, session_id: str, user_id: str = None) -> Dict[str, Any]:
        """Stop an active agent chat stream."""
        # Generate unique correlation ID
        correlation_id = self._generate_correlation_id(CorrelationIdPrefix.CHAT, session_id)

        # Prepare stop request
        stop_request = {
            "session_id": session_id,
            "user_id": user_id or "api_user",
            "action": "stop_stream",
        }

        # Send request
        await self.send_message(
            self.topics.AGENT_CHAT_STOP,
            stop_request,
            correlation_id,
        )

        logger.info(
            f"Stop stream request sent for session {session_id}",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "correlation_id": correlation_id,
            },
        )

        return {
            "success": True,
            "message": "Stop stream request sent successfully",
            "session_id": session_id,
        }

    async def execute_agent_task(
        self,
        agent_id: str,
        task: Dict[str, Any],
        workflow_ids: Optional[List[str]] = None,
        mcp_tool_ids: Optional[List[str]] = None,
        conversation_messages: List[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Execute a task with an agent."""
        # Generate unique correlation ID
        correlation_id = self._generate_correlation_id(CorrelationIdPrefix.TASK, agent_id)

        # Extract query from task
        query = task.get("query", task.get("input", {}).get("question", ""))

        # Prepare task request
        task_request = {
            "agent_id": agent_id,
            "query": query,
            "user_id": task.get("user_id", "test_user"),
            "organization_id": task.get("organization_id"),
        }

        # Add optional fields
        conversation_id = task.get("conversation_id")
        if conversation_id:
            task_request["conversation_id"] = conversation_id

        if workflow_ids:
            task_request["workflow_ids"] = workflow_ids

        if mcp_tool_ids:
            task_request["mcp_tool_ids"] = mcp_tool_ids

        # Store user message if conversation_id is provided
        if conversation_id and query:
            await self._store_user_task_message(
                conversation_id, query, task, agent_id, correlation_id
            )

        if conversation_messages:
            task_request["conversation_context"] = conversation_messages

        # Send request and wait for response
        await self.send_message(
            self.topics.AGENT_QUERY, task_request, correlation_id=correlation_id
        )

        logger.info(
            f"Sent agent task execution request with correlation ID {correlation_id} "
            f"for agent {agent_id}"
        )

        response = await self.wait_for_response(
            correlation_id=correlation_id, timeout=TimeoutConfig.TASK_EXECUTION
        )

        # Handle error response
        await self._handle_error_response(
            response, "execute agent task", correlation_id=correlation_id, agent_id=agent_id
        )

        session_key = f"correlation:{str(correlation_id)}"

        self.redis_service.set_data_to_redis(
            session_key,
            "correlation",
            response,
        )

        return response

    async def _store_user_task_message(
        self,
        conversation_id: str,
        query: str,
        task: Dict[str, Any],
        agent_id: str,
        correlation_id: str,
    ) -> None:
        """Store user task message in communication service."""
        try:
            user_id = task.get("user_id", "test_user")

            await self.communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_USER,
                data={"message": query},
                userId=user_id,
                status=MessageStatus.MESSAGE_STATUS_COMPLETED,
                type=MessageType.MESSAGE_TYPE_USER_MESSAGE,
            )

            logger.info(
                "User task message stored successfully",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "agent_id": agent_id,
                    "query_length": len(query),
                    "operation": "execute_agent_task",
                    "correlation_id": correlation_id,
                },
            )
        except Exception as e:
            logger.error(
                f"Error storing user task message: {str(e)}",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": task.get("user_id"),
                    "agent_id": agent_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "operation": "execute_agent_task",
                    "correlation_id": correlation_id,
                },
            )

    async def query_agent(
        self,
        agent_id: str,
        query: str,
        user_id: str = None,
        organization_id: str = None,
        variables: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """Query an agent directly without session creation."""
        # Generate unique correlation ID
        correlation_id = str(uuid.uuid4())

        # Prepare query request
        query_request = {
            "agent_id": agent_id,
            "query": query,
            "user_id": user_id or "api_user",
            "organization_id": organization_id,
            "variables": variables or {},
        }

        # Send request and wait for response
        await self.send_message(self.topics.AGENT_QUERY, query_request, correlation_id)
        response = await self.wait_for_response(
            correlation_id=correlation_id, timeout=TimeoutConfig.DEFAULT
        )

        # Handle error response
        await self._handle_error_response(response, "query agent", agent_id=agent_id)

        return response

    async def delete_agent_session(
        self,
        session_id: str,
        user_id: str = None,
        reason: str = "user_request",
        force: bool = False,
    ) -> Dict[str, Any]:
        """Delete an agent session."""
        # Prepare deletion request
        deletion_request = {
            "session_id": session_id,
            "user_id": user_id or "api_user",
            "reason": reason,
            "force": force,
        }

        # Send request and wait for response
        correlation_id = await self.send_message(
            self.topics.AGENT_SESSION_DELETION, deletion_request
        )
        response = await self.wait_for_response(correlation_id, timeout=TimeoutConfig.DEFAULT)

        # Handle error response
        await self._handle_error_response(response, "delete agent session", session_id=session_id)

        logger.info(
            f"Session deletion request processed for session {session_id}",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "reason": reason,
                "force": force,
                "success": response.get("success", False),
            },
        )

        return response

    async def create_orchestration_team_session(
        self,
        user_id: str,
        organization_id: str = None,
        conversation_messages: List[Dict[str, str]] = None,
        variables: Dict[str, Any] = None,
    ) -> str:
        """Create a new orchestration team session."""
        # Use semaphore to limit concurrent session creations
        async with self._session_semaphore:
            # Generate unique correlation ID without user_id to avoid conflicts
            # Use a unique UUID for each request to prevent concurrent request conflicts
            correlation_id = self._generate_correlation_id(CorrelationIdPrefix.ORCH_SESSION_CREATE)

            # Prepare creation request
            creation_request = {
                "user_id": user_id,
                "communication_type": "orchestration_team",
                "run_id": correlation_id,  # Use correlation_id as run_id for consistency
                "organization_id": organization_id,
                "variables": variables or {},
            }

            # Add conversation context if provided
            if conversation_messages:
                creation_request["conversation_context"] = conversation_messages

            # Send request and wait for response
            logger.info(
                f"Sending orchestration team session creation request with "
                f"correlation ID {correlation_id}"
            )

            # Send message with explicit correlation ID
            await self.send_message(
                self.topics.ORCHESTRATION_TEAM_SESSION, creation_request, correlation_id
            )

            logger.info(
                f"Message sent to topic {self.topics.ORCHESTRATION_TEAM_SESSION} with "
                f"correlation ID {correlation_id}"
            )

            logger.info(
                f"Waiting for orchestration team session response with "
                f"correlation ID {correlation_id}, timeout: {TimeoutConfig.SESSION_CREATION}s"
            )

            try:
                response = await self.wait_for_response(
                    correlation_id, timeout=TimeoutConfig.SESSION_CREATION
                )
            except asyncio.TimeoutError:
                logger.error(
                    f"Timeout waiting for orchestration team session response. "
                    f"Correlation ID: {correlation_id}, Timeout: {TimeoutConfig.SESSION_CREATION}s"
                )
                raise

            # Handle error response
            await self._handle_error_response(
                response,
                "create orchestration team session",
                user_id=user_id,
                correlation_id=correlation_id,
            )

            # Extract session ID
            session_id = response.get("session_id")
            if not session_id:
                logger.error(
                    f"No session ID in response for correlation ID {correlation_id}. "
                    f"Response: {response}"
                )
                raise ValueError("No session ID in response")

            logger.info(
                f"Orchestration team session created successfully with ID {session_id} "
                f"for correlation ID {correlation_id}"
            )
            return session_id

    async def send_orchestration_team_message(
        self,
        session_id: str,
        message: str,
        attachments: Optional[List] = None,
        mode: ChatMode = ChatMode.ASK,
        tools: Optional[str] = None,
        resource: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Send a message to the orchestration team."""
        # Use semaphore to limit concurrent message processing
        async with self._message_semaphore:
            # Generate unique correlation ID
            correlation_id = self._generate_correlation_id(CorrelationIdPrefix.CHAT, session_id)

            chat_request = {
                "session_id": session_id,
                "user_id": "api_user",
                "user_message": message,
                "organization_id": "api_gateway_org",
                "variables": {},
                "attachments": attachments or [],
                "mode": mode,
                "tools": tools,
                "resource": resource,
            }

            logger.info(
                f"Sending orchestration team message for session {session_id} with "
                f"correlation ID {correlation_id}"
            )

            # Send request and start streaming
            await self.send_message(
                self.topics.ORCHESTRATION_TEAM_CHAT, chat_request, correlation_id=correlation_id
            )

            # Start streaming for the session
            self.sse_manager.send_message_stream_started(client_id=session_id)

            # Wait for response
            response = await self.wait_for_response(
                correlation_id, timeout=TimeoutConfig.ORCHESTRATION_TEAM
            )

            # Handle error response
            await self._handle_error_response(
                response,
                "send orchestration team message",
                correlation_id=correlation_id,
                session_id=session_id,
            )

            return response

    async def send_human_input_response(
        self,
        session_id: str,
        team_conversation_id: str,
        user_input: str,
    ) -> Dict[str, Any]:
        """
        Send human input response to the orchestration team.

        Args:
            session_id: The session ID
            team_conversation_id: The team conversation ID requiring input
            user_input: The human's input or decision

        Returns:
            The response confirmation
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        response_request = {
            "session_id": session_id,
            "run_id": run_id,
            "team_conversation_id": team_conversation_id,
            "user_input": user_input,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("session_id", session_id.encode("utf-8")),
            ("team_conversation_id", team_conversation_id.encode("utf-8")),
        ]

        # Send request
        try:
            await self.producer.send_and_wait(
                self.topics.HUMAN_INPUT_RESPONSE,
                value=json.dumps(response_request).encode("utf-8"),
                headers=headers,
            )

            logger.info(f"Human input response sent for team conversation {team_conversation_id}")

            return {
                "success": True,
                "message": "Human input response sent successfully",
                "run_id": run_id,
            }
        except Exception as e:
            error_message = f"Failed to send human input response: {str(e)}"
            logger.error(error_message, extra={"error": str(e), "error_type": type(e).__name__})
            raise ValueError(error_message)

    def _determine_response_mode(self, agent_response: Dict[str, Any]) -> str:
        """
        Determine the response mode based on agent response source.

        Args:
            agent_response: The agent response data

        Returns:
            The appropriate response mode
        """
        source = agent_response.get("source")
        metadata = agent_response.get("metadata", {})

        # Use a mapping for better performance and maintainability
        source_mode_mapping = {
            Global_Employee.GENERAL_KNOWLEDGE_EMPLOYEE.value: ResponseMode.THINKING.value,
            Global_Employee.WEB_SEARCH_EMPLOYEE.value: ResponseMode.SEARCHING.value,
            Global_Employee.DISCOVERY_MASTER_EMPLOYEE.value: ResponseMode.DISCOVERY.value,
        }

        # Check for orchestrator delegation
        if source == "OrchestratorEmployee" and (
            metadata.get("content_type") == "delegation"
            or metadata.get("content_type") == "delegation_to_summary"
        ):
            return ResponseMode.THINKING.value

        # Return mapped mode or default
        return source_mode_mapping.get(source, ResponseMode.RESPONSE.value)

    async def _store_chat_messages(
        self,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        agent_response: Optional[Dict[str, Any]] = None,
        workflow_response: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Store user and agent messages in the communication service.

        For workflow responses, this function will:
        1. Check if a message already exists for this workflow in Redis
        2. If exists, update the existing message with new workflow response
        3. If not exists, create a new message and store message ID in Redis
        4. Update message status based on workflow event_type

        Args:
            session_id: The session ID
            correlation_id: The correlation ID for workflow responses
            agent_response: The agent's response data
            workflow_response: The workflow response data
        """
        try:
            # Get session data
            session_data, session_id = await self._get_session_data(session_id, correlation_id)
            if not session_data:
                return

            # Store agent response
            if agent_response:
                await self._store_agent_response(agent_response, session_data)

            # Handle workflow response
            if workflow_response and workflow_response.get("status") == "completed":
                await self._handle_workflow_response(workflow_response, session_data, session_id)

            logger.info(
                "Chat messages stored successfully in background",
                extra={
                    "session_id": session_id,
                    "agent_response_length": (len(agent_response) if agent_response else 0),
                    "workflow_response_present": bool(workflow_response),
                },
            )

        except Exception as e:
            logger.error(
                f"Error storing chat messages in background: {str(e)}",
                extra={
                    "session_id": session_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

    async def _get_session_data(
        self, session_id: Optional[str], correlation_id: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get session data from Redis."""

        workflow_id = None
        if not session_id and correlation_id:
            session_key = f"session_id:{str(correlation_id)}"
            session_data = self.redis_service.get_data_from_redis(session_key, "session")

            if not session_data:
                logger.warning(f"No session data found for correlation_id: {correlation_id}")
                return None, None
            session_id = session_data.get("session_id")
            workflow_id = session_data.get("workflow_id")

        if not session_id:
            return None, None

        # Get session data
        session_key = f"session:{session_id}"
        session_data = self.redis_service.get_data_from_redis(session_key, "data")

        if not session_data:
            logger.warning(f"No session data found for session_id: {session_id}")
            return None, None

        if workflow_id:
            session_data["workflow_id"] = workflow_id

        return session_data, session_id

    async def _store_agent_response(
        self, agent_response: Dict[str, Any], session_data: Dict[str, Any]
    ) -> None:
        """Store agent response message."""
        mode = self._determine_response_mode(agent_response)

        await self.communication_service.create_message(
            conversationId=session_data.get("conversation_id"),
            senderType=SenderType.SENDER_TYPE_ASSISTANT,
            data={
                "message": agent_response.get("content"),
                "metadata": agent_response.get("metadata"),
                "source": agent_response.get("source"),
                "mode": mode,
            },
            userId=session_data.get("user_id"),
            status=communication_schemas.MessageStatus.MESSAGE_STATUS_COMPLETED,
            type=communication_schemas.MessageType.MESSAGE_TYPE_CHAT,
        )

        # Extract token usage from agent response
        models_usage = agent_response.get("models_usage", {})
        input_tokens = models_usage.get("prompt_tokens", 0)
        output_tokens = models_usage.get("completion_tokens", 0)

        # Update conversation tokens
        await self.communication_service.update_conversation_tokens(
            conversationId=session_data.get("conversation_id"),
            userId=session_data.get("user_id"),
            inputTokens=input_tokens,
            outputTokens=output_tokens,
        )

    async def _handle_workflow_response(
        self,
        workflow_response: Dict[str, Any],
        session_data: Dict[str, Any],
        session_id: str,
    ) -> None:
        """Handle workflow response storage and updates."""
        workflow_message_key = f"workflow_message:{session_id}"
        workflow_id = workflow_response.get("workflow_id") or session_data.get("workflow_id")
        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        event_type = self.event_handler.determine_event_name(workflow_response)
        existing_message_id = self.redis_service.get_data_from_redis(
            workflow_message_key, "message_id"
        )
        message_status = self._get_message_status_from_event_type(event_type)

        if existing_message_id:
            await self._update_existing_workflow_message(
                existing_message_id, user_id, workflow_response, message_status, event_type
            )
        else:
            await self._create_new_workflow_message(
                conversation_id,
                user_id,
                workflow_id,
                workflow_response,
                message_status,
                event_type,
                workflow_message_key,
            )

    async def _update_existing_workflow_message(
        self,
        message_id: str,
        user_id: str,
        workflow_response: Dict[str, Any],
        message_status: communication_schemas.MessageStatus,
        event_type: str,
    ) -> None:
        """Update existing workflow message."""
        logger.info(
            f"Updating existing workflow message {message_id} with new response",
            extra={
                "message_id": message_id,
                "event_type": event_type,
                "workflow_id": workflow_response.get("workflow_id"),
            },
        )

        try:
            # Update the workflow response
            await self.communication_service.update_message_workflow_response(
                messageId=message_id,
                userId=user_id,
                newWorkflowResponse=workflow_response,
            )

            # Update message status based on event type
            await self.communication_service.update_message_status(
                messageId=message_id,
                userId=user_id,
                status=message_status,
            )

            logger.info(
                f"Successfully updated workflow message {message_id}",
                extra={
                    "message_id": message_id,
                    "status": message_status.value,
                    "event_type": event_type,
                },
            )

        except Exception as e:
            logger.error(
                f"Failed to update existing workflow message {message_id}: {str(e)}",
                extra={
                    "message_id": message_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

    async def _create_new_workflow_message(
        self,
        conversation_id: str,
        user_id: str,
        workflow_id: str,
        workflow_response: Dict[str, Any],
        message_status: communication_schemas.MessageStatus,
        event_type: str,
        workflow_message_key: str,
    ) -> None:
        """Create new workflow message."""
        logger.info(
            f"Creating new workflow message for conversation {conversation_id}",
            extra={
                "conversation_id": conversation_id,
                "workflow_id": workflow_id,
                "event_type": event_type,
            },
        )

        try:
            response = await self.communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_ASSISTANT,
                userId=user_id,
                workflowId=workflow_id,
                workflowResponse=[workflow_response],
                status=message_status,
                type=communication_schemas.MessageType.MESSAGE_TYPE_WORKFLOW,
            )

            message_id = response.get("id")

            # Store message ID in Redis for future updates
            self.redis_service.set_data_to_redis(
                workflow_message_key,
                "message_id",
                message_id,
            )

            logger.info(
                f"Successfully created new workflow message {message_id}",
                extra={
                    "message_id": message_id,
                    "conversation_id": conversation_id,
                    "workflow_id": workflow_id,
                    "status": message_status.value,
                    "event_type": event_type,
                },
            )

        except Exception as e:
            logger.error(
                f"Failed to create new workflow message: {str(e)}",
                extra={
                    "conversation_id": conversation_id,
                    "workflow_id": workflow_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

    def _get_message_status_from_event_type(
        self, event_type: str
    ) -> communication_schemas.MessageStatus:
        """
        Determine message status based on workflow event type.

        Args:
            event_type: The workflow event type

        Returns:
            MessageStatus: The corresponding message status
        """
        # Use sets for O(1) lookup performance
        completed_events = {
            SSEEventType.WORKFLOW_EXECUTION_COMPLETED.value,
            SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value,
            SSEEventType.MCP_EXECUTION_ENDED.value,
        }

        failed_events = {
            SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
            SSEEventType.WORKFLOW_EXECUTION_CANCELLED.value,
            SSEEventType.KNOWLEDGE_FETCH_FAILED.value,
            SSEEventType.MCP_EXECUTION_FAILED.value,
        }

        if event_type in completed_events or event_type in failed_events:
            # Mark as completed even if failed
            return communication_schemas.MessageStatus.MESSAGE_STATUS_COMPLETED
        else:
            # For started, step, and other ongoing events
            return communication_schemas.MessageStatus.MESSAGE_STATUS_RUNNING

    async def update_task(self, session_id: str) -> None:
        """
        Update task status to completed.

        Args:
            session_id: The session ID to update task for
        """
        try:
            # Get session data
            session_key = f"session:{session_id}"
            session_data = self.redis_service.get_data_from_redis(session_key, "data")

            if not session_data:
                logger.warning(f"No session data found for session_id: {session_id}")
                return

            task_id = session_data.get("task_id")
            user_id = session_data.get("user_id")

            if not task_id:
                return

            await self.communication_service.update_task_status(
                taskId=task_id,
                taskStatus=communication_schemas.TaskStatus.TASK_STATUS_COMPLETED,
                userId=user_id,
            )

            logger.info(
                f"Updated task {task_id} status to completed",
                extra={
                    "task_id": task_id,
                    "session_id": session_id,
                },
            )
        except Exception as e:
            logger.error(
                f"Failed to update task status for session {session_id}: {str(e)}",
                extra={
                    "session_id": session_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )


# Create a singleton instance
kafka_service = KafkaService()
