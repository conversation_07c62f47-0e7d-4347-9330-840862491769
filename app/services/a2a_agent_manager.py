"""
A2A Agent Manager - High-level service for managing agents via A2A protocol.
This service provides a unified interface for agent operations using the A2A client.
"""

from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from app.core.logging import get_logger
from app.schemas.a2a import AgentCard
from app.services.a2a_client import AutoGenA2AClient

logger = get_logger(__name__)


class A2AAgentManager:
    """
    High-level service for managing and interacting with agents via A2A protocol.

    This service provides:
    - Agent discovery and registration
    - Message routing to appropriate agents
    - Session management
    - Response aggregation
    - Error handling and retry logic
    """

    def __init__(self, base_url: str = "http://localhost:8000/api/v1/a2a"):
        """
        Initialize the A2A Agent Manager.

        Args:
            base_url: Base URL of the A2A server
        """
        self.base_url = base_url
        self.client: Optional[AutoGenA2AClient] = None
        self.agents_cache: Dict[str, Dict[str, Any]] = {}
        self.agent_cards_cache: Dict[str, AgentCard] = {}
        self.last_discovery: Optional[datetime] = None
        self.discovery_interval = 300  # 5 minutes

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def initialize(self):
        """Initialize the A2A client and discover agents."""
        if not self.client:
            self.client = AutoGenA2AClient(self.base_url)
            await self.client.__aenter__()

        # Initial agent discovery
        await self.refresh_agents()

    async def close(self):
        """Close the A2A client."""
        if self.client:
            await self.client.close()
            self.client = None

    async def refresh_agents(self) -> List[Dict[str, Any]]:
        """
        Refresh the list of available agents.

        Returns:
            List of agent information dictionaries
        """
        if not self.client:
            await self.initialize()

        try:
            agents = await self.client.discover_agents()

            # Update cache
            self.agents_cache = {agent.get("id"): agent for agent in agents if agent.get("id")}
            self.last_discovery = datetime.now()

            logger.info(f"Discovered {len(agents)} agents")
            return agents

        except Exception as e:
            logger.error(f"Error refreshing agents: {e}")
            return []

    async def get_agents(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get list of available agents.

        Args:
            force_refresh: Force refresh from server even if cache is recent

        Returns:
            List of agent information dictionaries
        """
        # Check if we need to refresh
        if (
            force_refresh
            or not self.last_discovery
            or (datetime.now() - self.last_discovery).seconds > self.discovery_interval
        ):
            await self.refresh_agents()

        return list(self.agents_cache.values())

    async def get_agent(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent information dictionary or None if not found
        """
        if agent_id not in self.agents_cache:
            await self.refresh_agents()

        return self.agents_cache.get(agent_id)

    async def get_agent_card(self, agent_id: str) -> Optional[AgentCard]:
        """
        Get the agent card for a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            AgentCard object or None if not found
        """
        if not self.client:
            await self.initialize()

        # Check cache first
        if agent_id in self.agent_cards_cache:
            return self.agent_cards_cache[agent_id]

        try:
            agent_card = await self.client.get_agent_card(agent_id)
            if agent_card:
                self.agent_cards_cache[agent_id] = agent_card
            return agent_card

        except Exception as e:
            logger.error(f"Error getting agent card for {agent_id}: {e}")
            return None

    async def send_message_to_agent(
        self,
        agent_id: str,
        message: str,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Send a message to a specific agent.

        Args:
            agent_id: ID of the target agent
            message: Message text to send
            session_id: Optional session ID for conversation tracking
            metadata: Optional metadata for the message

        Returns:
            Response dictionary or None if failed
        """
        if not self.client:
            await self.initialize()

        # Prepare metadata
        if metadata is None:
            metadata = {}

        if session_id:
            metadata["session_id"] = session_id

        metadata["timestamp"] = datetime.now().isoformat()

        try:
            response = await self.client.send_message(
                agent_id=agent_id, message_text=message, metadata=metadata
            )

            if response:
                logger.info(f"Message sent to {agent_id}, got response")
                return response
            else:
                logger.error(f"Failed to send message to {agent_id}")
                return None

        except Exception as e:
            logger.error(f"Error sending message to {agent_id}: {e}")
            return None

    async def send_streaming_message_to_agent(
        self,
        agent_id: str,
        message: str,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[AsyncGenerator[Dict[str, Any], None]]:
        """
        Send a message to an agent with streaming response.

        Args:
            agent_id: ID of the target agent
            message: Message text to send
            session_id: Optional session ID for conversation tracking
            metadata: Optional metadata for the message

        Returns:
            Async generator yielding response chunks or None if failed
        """
        if not self.client:
            await self.initialize()

        # Prepare metadata
        if metadata is None:
            metadata = {}

        if session_id:
            metadata["session_id"] = session_id

        metadata["timestamp"] = datetime.now().isoformat()

        try:
            stream = await self.client.send_message_streaming(
                agent_id=agent_id, message_text=message, metadata=metadata
            )

            if stream:
                logger.info(f"Started streaming message to {agent_id}")
                return stream
            else:
                logger.error(f"Failed to start streaming to {agent_id}")
                return None

        except Exception as e:
            logger.error(f"Error streaming message to {agent_id}: {e}")
            return None

    async def broadcast_message(
        self,
        message: str,
        agent_ids: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        Broadcast a message to multiple agents.

        Args:
            message: Message text to send
            agent_ids: List of agent IDs to send to (if None, sends to all agents)
            metadata: Optional metadata for the message

        Returns:
            Dictionary mapping agent_id to response (or None if failed)
        """
        if agent_ids is None:
            agents = await self.get_agents()
            agent_ids = [agent.get("id") for agent in agents if agent.get("id")]

        if not agent_ids:
            logger.warning("No agents available for broadcast")
            return {}

        logger.info(f"Broadcasting message to {len(agent_ids)} agents")

        # Send messages concurrently
        tasks = []
        for agent_id in agent_ids:
            task = self.send_message_to_agent(agent_id=agent_id, message=message, metadata=metadata)
            tasks.append((agent_id, task))

        # Wait for all responses
        results = {}
        for agent_id, task in tasks:
            try:
                response = await task
                results[agent_id] = response
            except Exception as e:
                logger.error(f"Error in broadcast to {agent_id}: {e}")
                results[agent_id] = None

        return results

    async def find_best_agent_for_task(
        self, task_description: str, required_capabilities: Optional[List[str]] = None
    ) -> Optional[str]:
        """
        Find the best agent for a given task based on capabilities and skills.

        Args:
            task_description: Description of the task
            required_capabilities: List of required capabilities

        Returns:
            Agent ID of the best match or None if no suitable agent found
        """
        agents = await self.get_agents()

        if not agents:
            return None

        # Simple scoring algorithm
        best_agent = None
        best_score = 0

        for agent in agents:
            agent_id = agent.get("id")
            if not agent_id:
                continue

            score = 0

            # Get agent card for detailed capabilities
            agent_card = await self.get_agent_card(agent_id)

            if agent_card:
                # Score based on capabilities
                if required_capabilities and agent_card.capabilities:
                    capability_matches = 0
                    for req_cap in required_capabilities:
                        # Simple string matching - could be improved
                        if any(
                            req_cap.lower() in str(cap).lower()
                            for cap in agent_card.capabilities.__dict__.values()
                            if cap
                        ):
                            capability_matches += 1
                    score += capability_matches * 10

                # Score based on skills
                if agent_card.skills:
                    for skill in agent_card.skills:
                        # Simple keyword matching in skill description
                        if any(
                            word.lower() in skill.description.lower()
                            for word in task_description.split()
                            if len(word) > 3
                        ):
                            score += 5

                # Score based on agent description
                agent_desc = agent.get("description", "")
                if any(
                    word.lower() in agent_desc.lower()
                    for word in task_description.split()
                    if len(word) > 3
                ):
                    score += 3

            if score > best_score:
                best_score = score
                best_agent = agent_id

        if best_agent:
            logger.info(f"Selected agent {best_agent} for task (score: {best_score})")
        else:
            logger.warning("No suitable agent found for task")

        return best_agent

    async def execute_task_with_best_agent(
        self,
        task_description: str,
        required_capabilities: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Execute a task by automatically selecting the best agent.

        Args:
            task_description: Description of the task to execute
            required_capabilities: List of required capabilities
            metadata: Optional metadata for the message

        Returns:
            Response dictionary or None if failed
        """
        # Find the best agent
        agent_id = await self.find_best_agent_for_task(task_description, required_capabilities)

        if not agent_id:
            logger.error("No suitable agent found for task")
            return None

        # Execute the task
        return await self.send_message_to_agent(
            agent_id=agent_id, message=task_description, metadata=metadata
        )
