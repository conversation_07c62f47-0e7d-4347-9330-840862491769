"""
A2A Client implementation for interacting with A2A-compliant agents.
This client can be used to communicate with agents that follow the A2A protocol.
"""

import asyncio
from typing import Any, Async<PERSON>enerator, Dict, List, Optional
from uuid import uuid4

import httpx

# Import A2A SDK components
from a2a.client import A2<PERSON>ardResolver, A2AClient, create_text_message_object
from a2a.types import (
    AgentCard,
    CancelTaskRequest,
    GetTaskRequest,
    Message,
    MessageSendParams,
    SendMessageRequest,
    SendStreamingMessageRequest,
    Task,
    TaskIdParams,
)

from app.core.logging import get_logger

logger = get_logger(__name__)


class AutoGenA2AClient:
    """
    A2A Client for interacting with AutoGen agents via the A2A protocol.

    This client provides high-level methods to:
    - Discover available agents
    - Send messages to agents
    - Stream responses from agents
    - Manage tasks (get status, cancel)
    """

    def __init__(self, base_url: str = "http://localhost:8000/api/v1/a2a"):
        """
        Initialize the A2A client.

        Args:
            base_url: Base URL of the A2A server
        """
        self.base_url = base_url
        self.httpx_client = httpx.AsyncClient()
        self.agent_clients: Dict[str, A2AClient] = {}
        self.agent_cards: Dict[str, AgentCard] = {}

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def close(self):
        """Close the HTTP client."""
        await self.httpx_client.aclose()

    async def discover_agents(self) -> List[Dict[str, Any]]:
        """
        Discover available agents from the A2A server.

        Returns:
            List of agent information dictionaries
        """
        try:
            response = await self.httpx_client.get(f"{self.base_url}/agents")
            response.raise_for_status()
            agents_data = response.json()

            if isinstance(agents_data, dict) and "agents" in agents_data:
                return agents_data["agents"]
            return agents_data if isinstance(agents_data, list) else []

        except Exception as e:
            logger.error(f"Error discovering agents: {e}")
            return []

    async def get_agent_card(self, agent_id: str) -> Optional[AgentCard]:
        """
        Get the agent card for a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            AgentCard object or None if not found
        """
        try:
            # Check cache first
            if agent_id in self.agent_cards:
                return self.agent_cards[agent_id]

            # Fetch agent card
            agent_card_url = f"{self.base_url}/agents/{agent_id}/.well-known/agent.json"
            response = await self.httpx_client.get(agent_card_url)
            response.raise_for_status()

            card_data = response.json()
            agent_card = AgentCard(**card_data)

            # Cache the agent card
            self.agent_cards[agent_id] = agent_card

            return agent_card

        except Exception as e:
            logger.error(f"Error getting agent card for {agent_id}: {e}")
            return None

    async def get_agent_client(self, agent_id: str) -> Optional[A2AClient]:
        """
        Get or create an A2A client for a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            A2AClient instance or None if agent not found
        """
        try:
            # Check cache first
            if agent_id in self.agent_clients:
                return self.agent_clients[agent_id]

            # Create client from agent card URL
            agent_base_url = f"{self.base_url}/agents/{agent_id}"
            client = await A2AClient.get_client_from_agent_card_url(
                self.httpx_client, agent_base_url
            )

            # Cache the client
            self.agent_clients[agent_id] = client

            return client

        except Exception as e:
            logger.error(f"Error creating client for agent {agent_id}: {e}")
            return None

    async def send_message(
        self,
        agent_id: str,
        message_text: str,
        metadata: Optional[Dict[str, Any]] = None,
        configuration: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Send a message to an agent and wait for completion.

        Args:
            agent_id: ID of the target agent
            message_text: Text message to send
            metadata: Optional metadata for the message
            configuration: Optional configuration for the request

        Returns:
            Response dictionary or None if failed
        """
        try:
            client = await self.get_agent_client(agent_id)
            if not client:
                logger.error(f"Could not get client for agent {agent_id}")
                return None

            # Create message object
            message = create_text_message_object(message_text)

            # Create request parameters
            params = MessageSendParams(
                message=message, metadata=metadata, configuration=configuration
            )

            # Create and send request
            request = SendMessageRequest(id=uuid4().hex, params=params)

            response = await client.send_message(request)

            # Convert response to dictionary
            return response.model_dump(mode="json", exclude_none=True)

        except Exception as e:
            logger.error(f"Error sending message to agent {agent_id}: {e}")
            return None

    async def send_message_streaming(
        self,
        agent_id: str,
        message_text: str,
        metadata: Optional[Dict[str, Any]] = None,
        configuration: Optional[Dict[str, Any]] = None,
    ) -> Optional[AsyncGenerator[Dict[str, Any], None]]:
        """
        Send a message to an agent with streaming response.

        Args:
            agent_id: ID of the target agent
            message_text: Text message to send
            metadata: Optional metadata for the message
            configuration: Optional configuration for the request

        Returns:
            Async generator yielding response chunks or None if failed
        """
        try:
            client = await self.get_agent_client(agent_id)
            if not client:
                logger.error(f"Could not get client for agent {agent_id}")
                return None

            # Create message object
            message = create_text_message_object(message_text)

            # Create request parameters
            params = MessageSendParams(
                message=message, metadata=metadata, configuration=configuration
            )

            # Create and send streaming request
            request = SendStreamingMessageRequest(id=uuid4().hex, params=params)

            stream_response = client.send_message_streaming(request)

            # Convert each chunk to dictionary
            async def response_generator():
                async for chunk in stream_response:
                    yield chunk.model_dump(mode="json", exclude_none=True)

            return response_generator()

        except Exception as e:
            logger.error(f"Error sending streaming message to agent {agent_id}: {e}")
            return None

    async def get_task(self, agent_id: str, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task.

        Args:
            agent_id: ID of the agent
            task_id: ID of the task

        Returns:
            Task status dictionary or None if failed
        """
        try:
            client = await self.get_agent_client(agent_id)
            if not client:
                logger.error(f"Could not get client for agent {agent_id}")
                return None

            # Create get task request
            request = GetTaskRequest(id=uuid4().hex, params=TaskIdParams(id=task_id))

            response = await client.get_task(request)

            # Convert response to dictionary
            return response.model_dump(mode="json", exclude_none=True)

        except Exception as e:
            logger.error(f"Error getting task {task_id} from agent {agent_id}: {e}")
            return None

    async def cancel_task(self, agent_id: str, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Cancel a running task.

        Args:
            agent_id: ID of the agent
            task_id: ID of the task to cancel

        Returns:
            Cancellation response dictionary or None if failed
        """
        try:
            client = await self.get_agent_client(agent_id)
            if not client:
                logger.error(f"Could not get client for agent {agent_id}")
                return None

            # Create cancel task request
            request = CancelTaskRequest(id=uuid4().hex, params=TaskIdParams(id=task_id))

            response = await client.cancel_task(request)

            # Convert response to dictionary
            return response.model_dump(mode="json", exclude_none=True)

        except Exception as e:
            logger.error(f"Error canceling task {task_id} from agent {agent_id}: {e}")
            return None
