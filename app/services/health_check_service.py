import asyncio
import time
from logging import Logger
from typing import Any, Dict

import grpc
import httpx
from aiokafka import AIOKafkaProducer

from app.core.config import settings
from app.core.logging import get_logger
from app.utils.redis.redis_client import RedisClient

logger: Logger = get_logger(__name__)


class HealthService:
    """Service for checking the health of microservices."""

    @staticmethod
    async def check_grpc_service(
        service_name: str, host: str, port: int
    ) -> Dict[str, Any]:
        """
        Check the health of a gRPC service.

        Args:
            service_name: Name of the service
            host: Host address of the service
            port: Port number of the service

        Returns:
            Dict[str, Any]: Health status of the service
        """
        start_time = time.time()
        try:
            # Try to establish a gRPC connection
            channel = grpc.insecure_channel(f"{host}:{port}")
            # Set a deadline for the connection attempt
            grpc.channel_ready_future(channel).result(timeout=5)
            
            # Close the channel
            channel.close()

            response_time = round((time.time() - start_time) * 1000, 2)

            return {
                "service": service_name,
                "status": "healthy",
                "host": host,
                "port": port,
                "response_time_ms": response_time,
                "type": "grpc",
            }
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            logger.error(
                "Error checking %s gRPC service health", service_name,
                extra={"error": str(e), "host": host, "port": port}
            )

            return {
                "service": service_name,
                "status": "unhealthy",
                "host": host,
                "port": port,
                "error": str(e),
                "response_time_ms": response_time,
                "type": "grpc",
            }

    @staticmethod
    async def check_http_service(
        service_name: str, url: str, timeout: int = 5
    ) -> Dict[str, Any]:
        """
        Check the health of an HTTP service.

        Args:
            service_name: Name of the service
            url: URL to check (should include health endpoint if available)
            timeout: Request timeout in seconds

        Returns:
            Dict[str, Any]: Health status of the service
        """
        start_time = time.time()
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                # Try to access the health endpoint or root endpoint
                if not url.endswith('/health'):
                    health_url = f"{url.rstrip('/')}/health"
                else:
                    health_url = url
                try:
                    response = await client.get(health_url)
                    status_code = response.status_code
                except httpx.HTTPStatusError:
                    # If health endpoint fails, try root endpoint
                    response = await client.get(url.rstrip('/'))
                    status_code = response.status_code

            response_time = round((time.time() - start_time) * 1000, 2)

            if 200 <= status_code < 300:
                return {
                    "service": service_name,
                    "status": "healthy",
                    "url": url,
                    "status_code": status_code,
                    "response_time_ms": response_time,
                    "type": "http",
                }
            else:
                return {
                    "service": service_name,
                    "status": "unhealthy",
                    "url": url,
                    "status_code": status_code,
                    "error": f"HTTP {status_code}",
                    "response_time_ms": response_time,
                    "type": "http",
                }

        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            logger.error(
                "Error checking %s HTTP service health", service_name,
                extra={"error": str(e), "url": url}
            )

            return {
                "service": service_name,
                "status": "unhealthy",
                "url": url,
                "error": str(e),
                "response_time_ms": response_time,
                "type": "http",
            }

    @staticmethod
    async def check_redis_service() -> Dict[str, Any]:
        """
        Check the health of Redis service.

        Returns:
            Dict[str, Any]: Health status of Redis
        """
        start_time = time.time()
        try:
            # Use the existing Redis client
            redis_client = RedisClient()
            client = redis_client.get_client()
            
            # Test Redis connection with ping
            result = client.ping()
            
            response_time = round((time.time() - start_time) * 1000, 2)

            if result:
                return {
                    "service": "redis",
                    "status": "healthy",
                    "host": settings.REDIS_HOST,
                    "port": settings.REDIS_PORT,
                    "response_time_ms": response_time,
                    "type": "redis",
                }
            else:
                return {
                    "service": "redis",
                    "status": "unhealthy",
                    "host": settings.REDIS_HOST,
                    "port": settings.REDIS_PORT,
                    "error": "Ping failed",
                    "response_time_ms": response_time,
                    "type": "redis",
                }

        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            logger.error(
                "Error checking Redis service health",
                extra={
                    "error": str(e),
                    "host": settings.REDIS_HOST,
                    "port": settings.REDIS_PORT
                }
            )

            return {
                "service": "redis",
                "status": "unhealthy",
                "host": settings.REDIS_HOST,
                "port": settings.REDIS_PORT,
                "error": str(e),
                "response_time_ms": response_time,
                "type": "redis",
            }

    @staticmethod
    async def check_kafka_service() -> Dict[str, Any]:
        """
        Check the health of Kafka service.

        Returns:
            Dict[str, Any]: Health status of Kafka
        """
        start_time = time.time()
        producer = None
        try:
            # Create a test producer to check Kafka connectivity
            producer = AIOKafkaProducer(
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                request_timeout_ms=5000,
                connections_max_idle_ms=5000,
            )
            
            # Start the producer (this will test connectivity)
            await producer.start()
            
            response_time = round((time.time() - start_time) * 1000, 2)

            return {
                "service": "kafka",
                "status": "healthy",
                "bootstrap_servers": settings.KAFKA_BOOTSTRAP_SERVERS,
                "response_time_ms": response_time,
                "type": "kafka",
            }

        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            logger.error(
                "Error checking Kafka service health",
                extra={
                    "error": str(e),
                    "bootstrap_servers": settings.KAFKA_BOOTSTRAP_SERVERS
                }
            )

            return {
                "service": "kafka",
                "status": "unhealthy",
                "bootstrap_servers": settings.KAFKA_BOOTSTRAP_SERVERS,
                "error": str(e),
                "response_time_ms": response_time,
                "type": "kafka",
            }
        finally:
            # Clean up producer
            if producer:
                try:
                    await producer.stop()
                except Exception:
                    pass  # Ignore cleanup errors

    @staticmethod
    async def check_user_service() -> Dict[str, Any]:
        """Check the health of the User Service."""
        return await HealthService.check_grpc_service(
            "user_service",
            settings.USER_SERVICE_HOST,
            settings.USER_SERVICE_PORT
        )

    @staticmethod
    async def check_communication_service() -> Dict[str, Any]:
        """Check the health of the Communication Service."""
        return await HealthService.check_grpc_service(
            "communication_service",
            settings.COMMUNICATION_SERVICE_HOST,
            settings.COMMUNICATION_SERVICE_PORT
        )

    @staticmethod
    async def check_agent_service() -> Dict[str, Any]:
        """Check the health of the Agent Service."""
        return await HealthService.check_http_service(
            "agent_service", settings.AGENT_SERVICE_URL
        )

    @staticmethod
    async def check_all_services() -> Dict[str, Any]:
        """
        Check the health of all microservices.

        Returns:
            Dict[str, Any]: Dictionary with overall status and services
        """
        # Define all service check methods
        service_checks = [
            HealthService.check_user_service,
            HealthService.check_communication_service,
            HealthService.check_agent_service,
            HealthService.check_redis_service,
            HealthService.check_kafka_service,
        ]

        # Run all checks concurrently for better performance
        try:
            services = await asyncio.gather(
                *[check() for check in service_checks], return_exceptions=True
            )
            
            # Handle any exceptions that occurred during health checks
            processed_services = []
            for i, result in enumerate(services):
                if isinstance(result, Exception):
                    # Create error response for failed health check
                    func_name = service_checks[i].__name__
                    service_name = func_name.replace('check_', '')
                    service_name = service_name.replace('_service', '')
                    processed_services.append({
                        "service": service_name,
                        "status": "unhealthy",
                        "error": f"Health check failed: {str(result)}",
                        "type": "unknown"
                    })
                else:
                    processed_services.append(result)
            
            services = processed_services
            
        except Exception as e:
            logger.error(f"Error running health checks: {str(e)}")
            return {
                "status": "unhealthy",
                "error": f"Failed to run health checks: {str(e)}",
                "services": [],
                "timestamp": time.time()
            }

        # Determine overall status
        overall_status = "healthy"
        healthy_count = 0
        total_count = len(services)
        
        for service in services:
            if service["status"] == "healthy":
                healthy_count += 1
            else:
                if healthy_count > 0:
                    overall_status = "degraded"
                else:
                    overall_status = "unhealthy"

        # Provide more detailed status information
        status_info = {
            "status": overall_status,
            "services": services,
            "summary": {
                "total": total_count,
                "healthy": healthy_count,
                "unhealthy": total_count - healthy_count,
                "health_percentage": (
                    round((healthy_count / total_count) * 100, 1)
                    if total_count > 0 else 0
                )
            },
            "timestamp": time.time()
        }

        return status_info

    @staticmethod
    async def check_specific_service(service_name: str) -> Dict[str, Any]:
        """
        Check the health of a specific microservice.

        Args:
            service_name: Name of the service to check

        Returns:
            Dict[str, Any]: Health status of the specified service
        """
        service_checks = {
            "user": HealthService.check_user_service,
            "user_service": HealthService.check_user_service,
            "communication": HealthService.check_communication_service,
            "communication_service": HealthService.check_communication_service,
            "agent": HealthService.check_agent_service,
            "agent_service": HealthService.check_agent_service,
            "redis": HealthService.check_redis_service,
            "kafka": HealthService.check_kafka_service,
        }

        if service_name not in service_checks:
            available_services = list(service_checks.keys())
            services_list = ', '.join(available_services)
            error_msg = f"Service not found. Available: {services_list}"
            return {
                "service": service_name,
                "status": "unknown",
                "error": error_msg,
                "available_services": available_services
            }

        try:
            return await service_checks[service_name]()
        except Exception as e:
            logger.error(f"Error checking {service_name} service: {str(e)}")
            return {
                "service": service_name,
                "status": "unhealthy",
                "error": f"Health check failed: {str(e)}"
            }
