"""
Event handler service for processing various SSE events.

This module provides a centralized event handler that processes different types
of events and routes them to the appropriate SSE manager methods.
"""

from typing import Any, Dict, Optional, Callable
import re
from dataclasses import dataclass
from app.core.config import settings
from app.core.logging import get_logger
from app.helper.sse_manager import SseManager
from app.shared.constants import ContentType, SSEEventType
from app.utils.redis.redis_service import RedisService

logger = get_logger(__name__)

WORKFLOW_REPLY_TOPIC = settings.WORKFLOW_REPLY_TOPIC

# Constants for default values
DEFAULT_CONTENT_TYPE = ContentType.TEXT_PLAIN.value
DEFAULT_MESSAGE_TYPE = "streaming_chunk"
DEFAULT_SOURCE = "assistant"
DEFAULT_REQUEST_ID = ""
DEFAULT_FETCH_ID = ""
DEFAULT_WORKFLOW_ID = ""
DEFAULT_CORRELATION_ID = ""

# Session key template
SESSION_KEY_TEMPLATE = "session_id:{}"

# Regex pattern for extracting client ID from correlation ID
CLIENT_ID_PATTERN = re.compile(r"chat[_\-]?([a-zA-Z0-9\-]+)")


@dataclass
class EventContext:
    """Context object for event processing."""

    topic: str
    correlation_id: str
    event_data: Dict[str, Any]
    event_type: Optional[str] = None
    session_id: Optional[str] = None
    client_id: Optional[str] = None


@dataclass
class ResultData:
    """Standard result data structure."""

    message: str = ""
    success: bool = True
    final: bool = False
    tool_name: str = ""


@dataclass
class ErrorData:
    """Standard error data structure."""

    message: str = ""
    success: bool = False
    final: bool = True
    tool_name: str = ""


class EventHandler:
    """
    Centralized event handler for processing SSE events.

    This class provides methods to handle different types of events
    and route them to the appropriate SSE manager methods.
    """

    def __init__(self, sse_manager: SseManager):
        """
        Initialize the event handler.

        Args:
            sse_manager: The SSE manager instance to use for sending events
        """
        self.sse_manager = sse_manager
        self.redis_service = RedisService()

        # Event handler mapping for better performance and maintainability
        self._event_handlers: Dict[str, Callable] = {
            SSEEventType.SESSION_INITIALIZED.value: self._handle_session_initialized,
            SSEEventType.MESSAGE_STREAM_STARTED.value: self._handle_message_stream_started,
            SSEEventType.MESSAGE_STREAMING.value: self._handle_message_streaming,
            SSEEventType.MESSAGE_END.value: self._handle_message_end,
            SSEEventType.MESSAGE_RESPONSE.value: self._handle_message_response,
            SSEEventType.MCP_EXECUTION_STARTED.value: self._handle_mcp_execution_started,
            SSEEventType.MCP_EXECUTION_ENDED.value: self._handle_mcp_execution_ended,
            SSEEventType.MCP_EXECUTION_FAILED.value: self._handle_mcp_execution_failed,
            SSEEventType.WORKFLOW_EXECUTION_STARTED.value: self._handle_workflow_execution_started,
            SSEEventType.WORKFLOW_EXECUTION_STEP.value: self._handle_workflow_execution_step,
            SSEEventType.WORKFLOW_EXECUTION_COMPLETED.value: self._handle_workflow_execution_completed,
            SSEEventType.WORKFLOW_EXECUTION_FAILED.value: self._handle_workflow_execution_failed,
            SSEEventType.KNOWLEDGE_FETCH_STARTED.value: self._handle_knowledge_fetch_started,
            SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value: self._handle_knowledge_fetch_completed,
            SSEEventType.KNOWLEDGE_FETCH_FAILED.value: self._handle_knowledge_fetch_failed,
            SSEEventType.ERROR.value: self._handle_error,
        }

    def _create_event_context(
        self, topic: str, correlation_id: str, event_data: Dict[str, Any]
    ) -> EventContext:
        """Create and populate event context."""
        context = EventContext(
            topic=topic,
            correlation_id=correlation_id,
            event_data=event_data,
            event_type=event_data.get("event_type"),
            session_id=event_data.get("session_id"),
        )

        # Determine client_id
        context.client_id = context.session_id or self._extract_client_id_from_correlation(
            correlation_id
        )

        return context

    def _extract_client_id_from_correlation(self, correlation_id: str) -> Optional[str]:
        """Extract client ID from correlation ID using regex, fallback to Redis lookup."""
        if not correlation_id:
            return None

        # First try regex pattern extraction
        match = CLIENT_ID_PATTERN.search(correlation_id)
        if match:
            return match.group(1)

        # If regex doesn't find session_id, try extracting from Redis using correlation_id
        try:
            session_key = f"correlation:{str(correlation_id)}"
            session_data = self.redis_service.get_data_from_redis(session_key, "correlation")

            if session_data and isinstance(session_data, dict):
                # Try to get session_id from Redis session data
                session_id = session_data.get("session_id")
                if session_id:
                    logger.debug(
                        f"Extracted session_id from Redis for correlation_id {correlation_id}: {session_id}"
                    )
                    return session_id

            logger.debug(f"No session data found in Redis for correlation_id: {correlation_id}")

        except Exception as e:
            logger.error(
                f"Error extracting client_id from Redis for correlation_id {correlation_id}: {e}"
            )

        return None

    def _get_session_data(self, correlation_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve session data from Redis."""
        if not correlation_id:
            return None

        session_key = SESSION_KEY_TEMPLATE.format(correlation_id)
        session_data = self.redis_service.get_data_from_redis(session_key, "session")

        print(f"_get_session_data session_data: {session_data}")

        if not session_data:
            logger.warning(f"No session data found for correlation_id: {correlation_id}")

        return session_data

    async def handle_event(
        self, topic: str, correlation_id: str, event_data: Dict[str, Any]
    ) -> bool:
        """
        Handle an event based on its type.

        Args:
            topic: The topic the event came from
            correlation_id: The correlation ID for tracking
            event_data: The event data containing event_type and other fields

        Returns:
            True if the event was handled, False otherwise
        """
        try:
            # Handle workflow reply topic specially
            if topic == WORKFLOW_REPLY_TOPIC and correlation_id:
                return await self._handle_workflow_reply(correlation_id, event_data)

            # Create event context
            context = self._create_event_context(topic, correlation_id, event_data)

            if not context.event_type:
                logger.warning("Event data missing event_type")
                return False

            if not context.client_id:
                logger.warning(f"No client_id found for event_type: {context.event_type}")
                return False

            # Use event handler mapping for better performance
            handler = self._event_handlers.get(context.event_type)
            if handler:
                return await handler(context)
            else:
                logger.warning(f"Unknown event type: {context.event_type}")
                return False

        except Exception as e:
            event_type = event_data.get("event_type", "unknown")
            logger.error(f"Error handling event {event_type}: {e}")
            return False

    async def _handle_workflow_reply(self, correlation_id: str, event_data: Dict[str, Any]) -> bool:
        """Handle workflow reply topic events."""
        session_data = self._get_session_data(correlation_id)

        if not session_data:
            return False

        client_id = session_data.get("session_id", correlation_id)
        event_name = self.determine_event_name(event_data)

        self.sse_manager.send_update(
            event_name=event_name,
            event_type="workflow",
            data=event_data,
            client_id=client_id,
        )

        return True

    # Session management handlers
    async def _handle_session_initialized(self, context: EventContext) -> bool:
        """Handle session initialized event."""
        if not context.session_id:
            return False

        self.sse_manager.send_session_initialized(
            session_id=context.session_id, client_id=context.client_id
        )
        return True

    # Message streaming handlers
    async def _handle_message_stream_started(self, context: EventContext) -> bool:
        """Handle message stream started event."""
        self.sse_manager.send_message_stream_started(client_id=context.client_id)
        return True

    async def _handle_message_streaming(self, context: EventContext) -> bool:
        """Handle message streaming event."""
        agent_response = context.event_data.get("agent_response", {})
        content_type = context.event_data.get("content_type", DEFAULT_CONTENT_TYPE)

        self.sse_manager.send_message_streaming(
            content=agent_response.get("content", ""),
            content_type=content_type,
            message_type=agent_response.get("message_type", DEFAULT_MESSAGE_TYPE),
            source=agent_response.get("source", DEFAULT_SOURCE),
            metadata=agent_response.get("metadata", {}),
            models_usage=agent_response.get("models_usage", {}),
            client_id=context.client_id,
        )
        return True

    async def _handle_message_end(self, context: EventContext) -> bool:
        """Handle message end event."""
        if not context.session_id:
            return False

        self.sse_manager.send_message_end(
            session_id=context.session_id, client_id=context.client_id
        )
        return True

    async def _handle_message_response(self, context: EventContext) -> bool:
        """Handle message response event."""
        content_type = context.event_data.get("content_type", DEFAULT_CONTENT_TYPE)

        self.sse_manager.send_message_response(
            content=context.event_data.get("agent_response", ""),
            content_type=content_type,
            client_id=context.client_id,
        )
        return True

    # MCP execution handlers
    async def _handle_mcp_execution_started(self, context: EventContext) -> bool:
        """Handle MCP execution started event."""

        data = {
            "request_id": context.event_data.get("request_id", DEFAULT_REQUEST_ID),
            "tool_name": context.event_data.get("tool_name", ""),
            "message": context.event_data.get("message", ""),
            "logo": context.event_data.get("logo", ""),
            "description": context.event_data.get("description", ""),
            "mcp_name": context.event_data.get("mcp_name", ""),
            "success": context.event_data.get("success", True),
            "status": "started",
        }

        self.sse_manager.send_mcp_execution_started(
            data=data,
            client_id=context.client_id,
        )
        return True

    async def _handle_mcp_execution_ended(self, context: EventContext) -> bool:
        """Handle MCP execution ended event."""
        data = {
            "request_id": context.event_data.get("request_id", DEFAULT_REQUEST_ID),
            "tool_name": context.event_data.get("tool_name", ""),
            "message": context.event_data.get("message", ""),
            "success": context.event_data.get("success", True),
            "final": context.event_data.get("final", False),
            "logo": context.event_data.get("logo", ""),
            "description": context.event_data.get("description", ""),
            "mcp_name": context.event_data.get("mcp_name", ""),
            "status": "completed",
        }

        self.sse_manager.send_mcp_execution_ended(data=data, client_id=context.client_id)
        return True

    async def _handle_mcp_execution_failed(self, context: EventContext) -> bool:
        """Handle MCP execution failed event."""

        data = {
            "request_id": context.event_data.get("request_id", DEFAULT_REQUEST_ID),
            "tool_name": context.event_data.get("tool_name", ""),
            "message": context.event_data.get("message", ""),
            "success": context.event_data.get("success", False),
            "final": context.event_data.get("final", True),
            "logo": context.event_data.get("logo", ""),
            "description": context.event_data.get("description", ""),
            "mcp_name": context.event_data.get("mcp_name", ""),
            "status": "failed",
        }

        self.sse_manager.send_mcp_execution_failed(data=data, client_id=context.client_id)
        return True

    # Workflow execution handlers
    async def _handle_workflow_execution_started(self, context: EventContext) -> bool:
        """Handle workflow execution started event."""
        workflow_id = context.event_data.get("workflow_id", DEFAULT_WORKFLOW_ID)
        correlation_id = context.event_data.get("correlation_id", DEFAULT_CORRELATION_ID)
        steps = context.event_data.get("steps", [])
        message = context.event_data.get("message", "")
        success = context.event_data.get("success", True)

        # Store session data in Redis
        session_key = SESSION_KEY_TEMPLATE.format(correlation_id)
        session_data = {"workflow_id": workflow_id, "session_id": context.client_id}

        result = self.redis_service.set_data_to_redis(
            session_key,
            "session",
            session_data,
        )

        logger.debug(f"Stored session data: {result} {session_data}")

        print(f"Stored session data: {result} {session_data}")

        data = {
            "workflow_id": workflow_id,
            "correlation_id": correlation_id,
            "message": message,
            "success": success,
            "steps": steps,
            "name": context.event_data.get("name", ""),
            "logo": context.event_data.get("logo", ""),
            "description": context.event_data.get("description", ""),
            "status": "started",
        }

        self.sse_manager.send_workflow_execution_started(
            data=data,
            client_id=context.client_id,
        )
        return True

    async def _handle_workflow_execution_step(self, context: EventContext) -> bool:
        """Handle workflow execution step event."""
        self.sse_manager.send_workflow_execution_step(
            workflow_id=context.event_data.get("workflow_id", DEFAULT_WORKFLOW_ID),
            correlation_id=context.event_data.get("correlation_id", DEFAULT_CORRELATION_ID),
            step_id=context.event_data.get("step_id", ""),
            step_name=context.event_data.get("step_name", ""),
            step_status=context.event_data.get("step_status", ""),
            result=context.event_data.get("result"),
            client_id=context.client_id,
        )
        return True

    async def _handle_workflow_execution_completed(self, context: EventContext) -> bool:
        """Handle workflow execution completed event."""
        self.sse_manager.send_workflow_execution_completed(
            workflow_id=context.event_data.get("workflow_id", DEFAULT_WORKFLOW_ID),
            correlation_id=context.event_data.get("correlation_id", DEFAULT_CORRELATION_ID),
            result=context.event_data.get("result"),
            client_id=context.client_id,
        )
        return True

    async def _handle_workflow_execution_failed(self, context: EventContext) -> bool:
        """Handle workflow execution failed event."""
        workflow_id = context.event_data.get("workflow_id", DEFAULT_WORKFLOW_ID)
        correlation_id = context.event_data.get("run_id", DEFAULT_CORRELATION_ID)
        data = {
            "workflow_id": workflow_id,
            "correlation_id": correlation_id,
            "tool_name": context.event_data.get("tool_name", ""),
            "message": context.event_data.get("message", ""),
            "success": context.event_data.get("success", False),
            "final": context.event_data.get("final", True),
            "logo": context.event_data.get("logo", ""),
            "description": context.event_data.get("description", ""),
            "status": "failed",
        }

        self.sse_manager.send_workflow_execution_failed(
            data=data,
            client_id=context.client_id,
        )
        return True

    # Knowledge fetch handlers
    async def _handle_knowledge_fetch_started(self, context: EventContext) -> bool:
        """Handle knowledge fetch started event."""
        fetch_id = context.event_data.get("run_id", DEFAULT_FETCH_ID)
        query = context.event_data.get("query") or context.event_data.get("message", "")

        data = {
            "query": query,
            "fetch_id": fetch_id,
            "status": "started",
        }

        self.sse_manager.send_knowledge_fetch_started(data=data, client_id=context.client_id)
        return True

    async def _handle_knowledge_fetch_completed(self, context: EventContext) -> bool:
        """Handle knowledge fetch completed event."""

        data = {
            "fetch_id": context.event_data.get("fetch_id", DEFAULT_FETCH_ID),
            "message": context.event_data.get("message", ""),
            "success": context.event_data.get("success", True),
            "final": context.event_data.get("final", False),
            "status": "completed",
        }

        self.sse_manager.send_knowledge_fetch_completed(data=data, client_id=context.client_id)
        return True

    async def _handle_knowledge_fetch_failed(self, context: EventContext) -> bool:
        """Handle knowledge fetch failed event."""

        data = {
            "fetch_id": context.event_data.get("run_id", DEFAULT_FETCH_ID),
            "tool_name": context.event_data.get("tool_name", ""),
            "message": context.event_data.get("message", ""),
            "success": context.event_data.get("success", False),
            "final": context.event_data.get("final", True),
            "status": "failed",
        }

        self.sse_manager.send_knowledge_fetch_failed(data, client_id=context.client_id)
        return True

    # Error handler
    async def _handle_error(self, context: EventContext) -> bool:
        """Handle error event."""
        self.sse_manager.send_error(
            error_code=context.event_data.get("error_code", ""),
            error_message=context.event_data.get("message", ""),
            details=context.event_data.get("details", ""),
            client_id=context.client_id,
        )
        return True

    def determine_event_name(self, response_data: Dict[str, Any]) -> str:
        """
        Determine the appropriate event name based on response data content.

        Args:
            response_data: The response data dictionary

        Returns:
            str: The event name to use for this response
        """
        workflow_status = response_data.get("workflow_status")
        if not workflow_status:
            return SSEEventType.WORKFLOW_EXECUTION_STEP.value

        # Use mapping for better performance
        status_mapping = {
            "completed": SSEEventType.WORKFLOW_EXECUTION_COMPLETED.value,
            "failed": SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
            "cancelled": SSEEventType.WORKFLOW_EXECUTION_CANCELLED.value,
        }

        return status_mapping.get(workflow_status, SSEEventType.WORKFLOW_EXECUTION_STEP.value)
