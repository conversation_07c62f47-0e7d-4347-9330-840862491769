from enum import Enum


class ChatMode(str, Enum):
    """Enumeration of chat message modes."""

    ASK = "ASK"
    ACT = "ACT"


class Resources(str, Enum):

    ALL = "ALL"
    RESEARCH = "RESEARCH"
    ORGANIZATION = "ORGANIZATION"


class ResponseMode(str, Enum):
    THINKING = "thinking"
    PROCESSING = "processing"
    RESPONSE = "RESPONSE"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SEARCHING = "searching"
    DISCOVERY = "discovery"
