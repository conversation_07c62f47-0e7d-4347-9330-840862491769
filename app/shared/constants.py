from enum import Enum


# Enum for channel types
class ChannelType(str, Enum):
    CHANNEL_TYPE_UNSPECIFIED = "CHANNEL_TYPE_UNSPECIFIED"
    CHANNEL_TYPE_WEB = "CHANNEL_TYPE_WEB"


# Mapping dictionaries for conversion between string and int
CHANNEL_TYPE_TO_INT = {
    ChannelType.CHANNEL_TYPE_UNSPECIFIED: 0,
    ChannelType.CHANNEL_TYPE_WEB: 1,
}

INT_TO_CHANNEL_TYPE = {
    0: ChannelType.CHANNEL_TYPE_UNSPECIFIED,
    1: ChannelType.CHANNEL_TYPE_WEB,
}


# Enum for sender type
class SenderType(str, Enum):
    SENDER_TYPE_UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED"
    SENDER_TYPE_USER = "SENDER_TYPE_USER"
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Mapping dictionaries for sender type
SENDER_TYPE_TO_INT = {
    SenderType.SENDER_TYPE_UNSPECIFIED: 0,
    SenderType.SENDER_TYPE_USER: 1,
    SenderType.SENDER_TYPE_ASSISTANT: 2,
}

INT_TO_SENDER_TYPE = {
    0: SenderType.SENDER_TYPE_UNSPECIFIED,
    1: SenderType.SENDER_TYPE_USER,
    2: SenderType.SENDER_TYPE_ASSISTANT,
}


# Enum for message status
class MessageStatus(str, Enum):
    MESSAGE_STATUS_UNSPECIFIED = "MESSAGE_STATUS_UNSPECIFIED"
    MESSAGE_STATUS_RUNNING = "MESSAGE_STATUS_RUNNING"
    MESSAGE_STATUS_COMPLETED = "MESSAGE_STATUS_COMPLETED"


# Mapping dictionaries for message status
MESSAGE_STATUS_TO_INT = {
    MessageStatus.MESSAGE_STATUS_UNSPECIFIED: 0,
    MessageStatus.MESSAGE_STATUS_RUNNING: 1,
    MessageStatus.MESSAGE_STATUS_COMPLETED: 2,
}

INT_TO_MESSAGE_STATUS = {
    0: MessageStatus.MESSAGE_STATUS_UNSPECIFIED,
    1: MessageStatus.MESSAGE_STATUS_RUNNING,
    2: MessageStatus.MESSAGE_STATUS_COMPLETED,
}


# Enum for message type
class MessageType(str, Enum):
    MESSAGE_TYPE_UNSPECIFIED = "MESSAGE_TYPE_UNSPECIFIED"
    MESSAGE_TYPE_CHAT = "MESSAGE_TYPE_CHAT"
    MESSAGE_TYPE_MCP = "MESSAGE_TYPE_MCP"
    MESSAGE_TYPE_WORKFLOW = "MESSAGE_TYPE_WORKFLOW"
    MESSAGE_TYPE_USER_MESSAGE = "MESSAGE_TYPE_USER_MESSAGE"


# Mapping dictionaries for message type
MESSAGE_TYPE_TO_INT = {
    MessageType.MESSAGE_TYPE_UNSPECIFIED: 0,
    MessageType.MESSAGE_TYPE_CHAT: 1,
    MessageType.MESSAGE_TYPE_MCP: 2,
    MessageType.MESSAGE_TYPE_WORKFLOW: 3,
    MessageType.MESSAGE_TYPE_USER_MESSAGE: 4,
}

INT_TO_MESSAGE_TYPE = {
    0: MessageType.MESSAGE_TYPE_UNSPECIFIED,
    1: MessageType.MESSAGE_TYPE_CHAT,
    2: MessageType.MESSAGE_TYPE_MCP,
    3: MessageType.MESSAGE_TYPE_WORKFLOW,
    4: MessageType.MESSAGE_TYPE_USER_MESSAGE,
}


# Enum for chat types
class ChatType(str, Enum):
    CHAT_TYPE_UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED"
    CHAT_TYPE_AGENT = "CHAT_TYPE_AGENT"
    CHAT_TYPE_GLOBAL = "CHAT_TYPE_GLOBAL"


# Mapping dictionaries for chat type
CHAT_TYPE_TO_INT = {
    ChatType.CHAT_TYPE_UNSPECIFIED: 0,
    ChatType.CHAT_TYPE_AGENT: 1,
    ChatType.CHAT_TYPE_GLOBAL: 2,
}

INT_TO_CHAT_TYPE = {
    0: ChatType.CHAT_TYPE_UNSPECIFIED,
    1: ChatType.CHAT_TYPE_AGENT,
    2: ChatType.CHAT_TYPE_GLOBAL,
}


# Enum for task statuses
class TaskStatus(str, Enum):
    TASK_STATUS_UNSPECIFIED = "TASK_STATUS_UNSPECIFIED"
    TASK_STATUS_RUNNING = "TASK_STATUS_RUNNING"
    TASK_STATUS_COMPLETED = "TASK_STATUS_COMPLETED"
    TASK_STATUS_FAILED = "TASK_STATUS_FAILED"
    TASK_STATUS_PAUSED = "TASK_STATUS_PAUSED"
    TASK_STATUS_CANCELLED = "TASK_STATUS_CANCELLED"


# Mapping dictionaries for task status
TASK_STATUS_TO_INT = {
    TaskStatus.TASK_STATUS_UNSPECIFIED: 0,
    TaskStatus.TASK_STATUS_RUNNING: 1,
    TaskStatus.TASK_STATUS_COMPLETED: 2,
    TaskStatus.TASK_STATUS_FAILED: 3,
    TaskStatus.TASK_STATUS_PAUSED: 4,
    TaskStatus.TASK_STATUS_CANCELLED: 5,
}

INT_TO_TASK_STATUS = {
    0: TaskStatus.TASK_STATUS_UNSPECIFIED,
    1: TaskStatus.TASK_STATUS_RUNNING,
    2: TaskStatus.TASK_STATUS_COMPLETED,
    3: TaskStatus.TASK_STATUS_FAILED,
    4: TaskStatus.TASK_STATUS_PAUSED,
    5: TaskStatus.TASK_STATUS_CANCELLED,
}


# Enum for conversation types
class ConversationType(str, Enum):
    """Types of conversations for different interaction modes."""

    STANDARD = "standard"
    MCP = "mcp"
    WORKFLOW = "workflow"
    STREAMING = "streaming"


# Enum for content types
class ContentType(str, Enum):
    """Content types for messages and responses."""

    TEXT_PLAIN = "text/plain"
    TEXT_HTML = "text/html"
    TEXT_MARKDOWN = "text/markdown"
    APPLICATION_JSON = "application/json"
    IMAGE_JPEG = "image/jpeg"
    IMAGE_PNG = "image/png"
    IMAGE_GIF = "image/gif"
    IMAGE_WEBP = "image/webp"
    AUDIO_MP3 = "audio/mp3"
    AUDIO_WAV = "audio/wav"
    VIDEO_MP4 = "video/mp4"
    APPLICATION_PDF = "application/pdf"
    APPLICATION_OCTET_STREAM = "application/octet-stream"


# ===== SSE EVENT TYPES =====


# Enum for SSE event types based on chat response structure
class SSEEventType(str, Enum):
    """Server-Sent Events types for real-time communication."""

    # Session management events
    SESSION_INITIALIZED = "session_initialized"

    # Message streaming events
    MESSAGE_STREAM_STARTED = "message_stream_started"
    MESSAGE_STREAMING = "message_streaming"
    MESSAGE_END = "message_end"

    MESSAGE_RESPONSE = "message_response"

    # MCP (Model Context Protocol) events
    MCP_EXECUTION_STARTED = "mcp_execution_started"
    MCP_EXECUTION_ENDED = "mcp_execution_ended"
    MCP_EXECUTION_FAILED = "mcp_execution_failed"

    # Workflow execution events
    WORKFLOW_EXECUTION_STARTED = "workflow_execution_started"
    WORKFLOW_EXECUTION_STEP = "workflow_execution_step"
    WORKFLOW_EXECUTION_COMPLETED = "workflow_execution_completed"
    WORKFLOW_EXECUTION_FAILED = "workflow_execution_failed"
    WORKFLOW_EXECUTION_CANCELLED = "workflow_execution_cancelled"

    KNOWLEDGE_FETCH_STARTED = "knowledge_fetch_started"
    KNOWLEDGE_FETCH_COMPLETED = "knowledge_fetch_completed"
    KNOWLEDGE_FETCH_FAILED = "knowledge_fetch_failed"

    # Task delegation events
    TASK_DELEGATION_SUCCESS = "task_delegation_success"
    TASK_DELEGATION_FAILED = "task_delegation_failed"

    # System events
    KEEP_ALIVE = "keep_alive"
    ERROR = "error"
    CONNECTION_CLOSED = "connection_closed"


# ===== SSE EVENT CONSTANTS =====

# Session initialization event data structure
SESSION_INITIALIZED_EVENT = {
    "event": SSEEventType.SESSION_INITIALIZED,
    "data": {
        "session_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "initialized",
    },
}

# Message streaming start event
MESSAGE_STREAM_STARTED_EVENT = {
    "event": SSEEventType.MESSAGE_STREAM_STARTED,
    "data": {"timestamp": None, "status": "started"},  # To be filled
}

# Message streaming chunk template
MESSAGE_STREAMING_EVENT_TEMPLATE = {
    "event": SSEEventType.MESSAGE_STREAMING,
    "data": {
        "message_id": None,  # To be filled dynamically
        "content": None,  # To be filled dynamically
        "content_type": ContentType.TEXT_PLAIN,
        "conversation_type": ConversationType.STANDARD,
        "chunk_index": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
    },
}

# Message end event
MESSAGE_END_EVENT = {
    "event": SSEEventType.MESSAGE_END,
    "data": {
        "message_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "completed",
    },
}

# MCP execution events
MCP_EXECUTION_STARTED_EVENT = {
    "event": SSEEventType.MCP_EXECUTION_STARTED,
    "data": {
        "execution_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "started",
    },
}

MCP_EXECUTION_ENDED_EVENT = {
    "event": SSEEventType.MCP_EXECUTION_ENDED,
    "data": {
        "execution_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "completed",
        "result": None,  # To be filled dynamically
    },
}

# Workflow execution events
WORKFLOW_EXECUTION_STARTED_EVENT = {
    "event": SSEEventType.WORKFLOW_EXECUTION_STARTED,
    "data": {
        "workflow_id": None,  # To be filled dynamically
        "execution_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "started",
        "steps": [],  # To be filled dynamically
    },
}

WORKFLOW_EXECUTION_STEP_EVENT_TEMPLATE = {
    "event": SSEEventType.WORKFLOW_EXECUTION_STEP,
    "data": {
        "workflow_id": None,  # To be filled dynamically
        "execution_id": None,  # To be filled dynamically
        "step_id": None,  # To be filled dynamically
        "step_name": None,  # To be filled dynamically
        "step_status": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "result": None,  # To be filled dynamically
    },
}

# Keep alive event
KEEP_ALIVE_EVENT = {"event": SSEEventType.KEEP_ALIVE, "data": "keep_alive"}

# Error event template
ERROR_EVENT_TEMPLATE = {
    "event": SSEEventType.ERROR,
    "data": {
        "error_code": None,  # To be filled dynamically
        "error_message": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "details": None,  # To be filled dynamically
    },
}

# Task delegation events
TASK_DELEGATION_SUCCESS_EVENT = {
    "event": SSEEventType.TASK_DELEGATION_SUCCESS,
    "data": {
        "task_id": None,  # To be filled dynamically
        "session_id": None,  # To be filled dynamically
        "agent_session_id": None,  # To be filled dynamically
        "conversation_id": None,  # To be filled dynamically
        "agent_id": None,  # To be filled dynamically
        "global_session_id": None,  # To be filled dynamically
        "correlation_id": None,  # To be filled dynamically
        "title": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "success",
    },
}

TASK_DELEGATION_FAILED_EVENT = {
    "event": SSEEventType.TASK_DELEGATION_FAILED,
    "data": {
        "error_code": None,  # To be filled dynamically
        "error_message": None,  # To be filled dynamically
        "correlation_id": None,  # To be filled dynamically
        "global_session_id": None,  # To be filled dynamically
        "timestamp": None,  # To be filled dynamically
        "status": "failed",
    },
}
