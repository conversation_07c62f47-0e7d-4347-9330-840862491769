from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON>2P<PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from jose import JWTError, jwt
from app.core.config import settings
from dotenv import load_dotenv


load_dotenv()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a new JWT access token.
    
    Args:
        data: The data to encode in the token
        expires_delta: Optional expiration time delta
        
    Returns:
        The encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict) -> str:
    """
    Create a new JWT refresh token.
    
    Args:
        data: The data to encode in the token
        
    Returns:
        The encoded JWT token
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Validate and decode the JWT token to get the current user.
    
    Args:
        token: The JWT token
        
    Returns:
        The decoded user data
        
    Raises:
        HTTPException: If the token is invalid
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        if not payload.get("sub"):
            raise credentials_exception
        return payload
    except JWTError:
        raise credentials_exception


async def validate_api_key(api_key: str = Depends(api_key_header)) -> Dict[str, Any]:
    """
    Validate the API key and return the associated user/project information.
    
    Args:
        api_key: The API key from the request header
        
    Returns:
        Dictionary with user and project information
        
    Raises:
        HTTPException: If the API key is invalid or missing
    """
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required",
            headers={"WWW-Authenticate": "APIKey"},
        )
    
    # Here you would typically validate the API key against your database
    # For now, we'll just return a placeholder
    # In a real implementation, you would:
    # 1. Check if the API key exists in your database
    # 2. Verify it's not expired
    # 3. Check if it has the necessary permissions
    # 4. Return the associated user/project data
    
    # Placeholder implementation
    return {
        "api_key": api_key,
        "user_id": "user_placeholder",
        "project_id": "project_placeholder",
        "tier": "free",
        "scopes": ["read", "write"],
    }
