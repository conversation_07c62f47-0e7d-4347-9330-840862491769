from typing import Any, Dict, List, Optional

from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "developer-api-gateway"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"

    # Add the missing fields
    REPO_URL: Optional[str] = None
    GIT_TOKEN: Optional[str] = None

    # A2A and AutoGen settings
    A2A_BASE_URL: str = "http://localhost:8000"

    # Kafka settings
    KAFKA_BOOTSTRAP_SERVERS: str = "localhost:9092"
    KAFKA_AGENT_CREATION_TOPIC: str = "agent_creation_requests"
    KAFKA_AGENT_CHAT_TOPIC: str = "agent_chat_requests"
    KAFKA_AGENT_RESPONSE_TOPIC: str = "agent_chat_responses"
    KAFKA_AGENT_TASK_TOPIC: str = "agent_task_requests"
    KAFKA_AGENT_QUERY_TOPIC: str = "agent_query_requests"
    KAFKA_AGENT_SESSION_DELETION_TOPIC: str = "agent_session_deletion_requests"
    KAFKA_AGENT_CHAT_STOP_TOPIC: str = "agent_chat_stop_requests"

    # Orchestration Team Kafka Topics
    KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC: str = "orchestration_team_session_requests"
    KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC: str = "orchestration_team_chat_requests"
    KAFKA_HUMAN_INPUT_RESPONSE_TOPIC: str = "human_input_responses"
    KAFKA_HUMAN_INPUT_REQUEST_TOPIC: str = "human_input_requests"

    WORKFLOW_REPLY_TOPIC: str = "workflow-responses"

    # Enhanced Logging Configuration
    LOG_LEVEL: str = "DEBUG"  # Fallback for single level mode
    LOG_LEVELS: Optional[str] = None  # Multi-level support: "DEBUG,ERROR,INFO"
    LOG_FORMAT: str = "text"  # "json" or "text"
    LOG_INCLUDE_SOURCE: bool = False  # Include source code location in text format
    LOG_PERFORMANCE_MODE: bool = False  # Include performance metrics
    LOG_ASYNC_ENABLED: bool = False  # Enable asynchronous logging

    # Logger-specific configuration
    LOGGERS: Dict[str, str] = {
        "app": "INFO",
        "uvicorn": "INFO",
        "fastapi": "INFO",
        "aiokafka": "WARNING",
        "sqlalchemy": "WARNING",
        "httpx": "WARNING",
        "asyncio": "WARNING",
        "app.api.routers": "INFO",
        "app.services": "INFO",
        "app.middleware": "INFO",
        "app.core": "INFO",
    }

    # Service endpoints
    USER_SERVICE_HOST: str = "user_service"
    USER_SERVICE_PORT: int = 50052

    COMMUNICATION_SERVICE_HOST: str = "localhost"
    COMMUNICATION_SERVICE_PORT: int = 50055

    # Agent Service gRPC Configuration
    AGENT_SERVICE_HOST: str = "localhost"
    AGENT_SERVICE_PORT: int = 50057

    AGENT_SERVICE_URL: str = "http://agent-service:8000"

    AGENT_SERVICE_GRPC_ENABLED: bool = False  # Disable gRPC by default

    # Redis settings
    REDIS_HOST: str = ""
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    REDIS_JWT_ACCESS_EXPIRE_SEC: int = 3600
    REDIS_URI: Optional[str] = None

    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"REDIS_HOST", "REDIS_PORT", "REDIS_DB"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required Redis configuration: {missing}")

        auth_part = f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT', 6379)}/{values.get('REDIS_DB', 0)}"

    # JWT settings
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # CORS settings
    CORS_ORIGINS: str = "*"
    CORS_CREDENTIALS: bool = True
    # CORS_METHODS: List[str] = ["*"]
    # CORS_HEADERS: List[str] = ["*"]

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # This will ignore extra fields


settings = Settings()
