import queue
import threading
import time
from typing import Any, Dict, Optional


from app.core.logging import get_logger
from app.shared.constants import ContentType, SSEEventType

logger = get_logger(__name__)


class ClientInfo:
    """Information about a connected SSE client."""

    def __init__(self, client_id: str, client_queue: queue.Queue, max_queue_size: int = 200):
        self.client_id = client_id
        self.client_queue = client_queue
        self.max_queue_size = max_queue_size
        self.connected_at = time.time()
        self.last_activity = time.time()
        self.events_sent = 0
        self.events_dropped = 0
        self.queue_full_count = 0


class SseManager:
    """
    Advanced Server-Sent Events (SSE) management class with enhanced queue
    management.

    Features:
    - Dynamic queue sizing with backpressure handling
    - Event prioritization and intelligent dropping
    - Client health monitoring and automatic cleanup
    - Comprehensive metrics and logging
    """

    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super(SseManager, cls).__new__(cls)
            cls._instance.initialize()
        return cls._instance

    def initialize(self):
        # Thread-safe dictionary for connected clients
        self.connected_clients: Dict[str, ClientInfo] = {}
        self.clients_lock = threading.RLock()  # Use RLock for nested locking

        # Configuration
        self.default_queue_size = 200  # Increased from 100
        self.max_queue_size = 500  # Maximum allowed queue size

        self.cleanup_interval = 300  # 5 minutes
        self.max_idle_time = 1800  # 30 minutes

        # Shutdown flag
        self.is_shutting_down = False

        # Start cleanup thread
        self._start_cleanup_thread()

    def _start_cleanup_thread(self):
        """Start background thread for client cleanup."""

        def cleanup_worker():
            while not self.is_shutting_down:
                try:
                    self._cleanup_idle_clients()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"Error in SSE cleanup thread: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    def _cleanup_idle_clients(self):
        """Remove idle clients that haven't been active."""
        current_time = time.time()
        clients_to_remove = []

        with self.clients_lock:
            for client_id, client_info in self.connected_clients.items():
                idle_time = current_time - client_info.last_activity
                if idle_time > self.max_idle_time:
                    clients_to_remove.append(client_id)

        for client_id in clients_to_remove:
            logger.info(f"Removing idle SSE client: {client_id}")
            self.remove_client(client_id)

    def _handle_queue_full(self, client_info: ClientInfo, new_event: dict) -> bool:
        """
        Handle queue full situation - NEVER skip new events.
        Always make room for new events by dropping older ones.

        Returns:
            bool: True if event was successfully queued, False otherwise
        """
        client_info.queue_full_count += 1

        try:
            # OPTIMAL APPROACH: Always prioritize new events - never skip them
            # Remove oldest events until we can add the new one
            events_dropped_count = 0
            while client_info.client_queue.full():
                try:
                    dropped_event = client_info.client_queue.get_nowait()
                    events_dropped_count += 1
                    logger.debug(
                        f"Dropped oldest event (priority: "
                        f"{dropped_event.get('priority', 'unknown')}) for client "
                        f"{client_info.client_id} to make room for new event"
                    )
                except queue.Empty:
                    break

            # Update dropped events counter
            client_info.events_dropped += events_dropped_count

            # Now add the new event - this should always succeed
            client_info.client_queue.put_nowait(new_event)
            logger.debug(
                f"Successfully queued new event for client "
                f"{client_info.client_id} after dropping "
                f"{events_dropped_count} old events"
            )
            return True

        except Exception as e:
            logger.error(f"Error handling queue full for client " f"{client_info.client_id}: {e}")
            # As a last resort, try to force add the event by clearing space
            try:
                # Clear half the queue to make room
                cleared_count = 0
                queue_size = client_info.client_queue.qsize()
                target_clear = max(1, queue_size // 2)

                for _ in range(target_clear):
                    try:
                        client_info.client_queue.get_nowait()
                        cleared_count += 1
                    except queue.Empty:
                        break

                client_info.events_dropped += cleared_count
                client_info.client_queue.put_nowait(new_event)
                logger.warning(
                    f"Emergency queue clear for client "
                    f"{client_info.client_id}: cleared {cleared_count} events "
                    f"to make room for new event"
                )
                return True
            except (queue.Full, queue.Empty):
                logger.error(
                    f"CRITICAL: Failed to queue new event for client "
                    f"{client_info.client_id} even after emergency clear"
                )

        return False

    def _handle_queue_full_fallback(self, client_info: ClientInfo, new_event: dict) -> bool:
        """Fallback strategy: drop oldest event."""
        try:
            client_info.client_queue.get_nowait()
            client_info.client_queue.put_nowait(new_event)
            return True
        except (queue.Empty, queue.Full):
            return False

    def add_client(self, client_id: str, client_queue: queue.Queue, max_queue_size: int = None):
        """
        Register a new client connection with enhanced monitoring.
        Prevents duplicate client additions.

        Args:
            client_id: Unique identifier for the client
            client_queue: Queue for sending events to the client
            max_queue_size: Maximum queue size (uses default if None)

        Returns:
            queue.Queue: The client queue (existing or newly created)
        """
        if max_queue_size is None:
            max_queue_size = self.default_queue_size

        with self.clients_lock:
            # Check if client is already connected
            if client_id in self.connected_clients:
                logger.warning(
                    f"Client {client_id} is already connected. Skipping duplicate addition."
                )
                return self.connected_clients[client_id].client_queue

            client_info = ClientInfo(client_id, client_queue, max_queue_size)
            self.connected_clients[client_id] = client_info

        logger.info(f"New SSE client connected: {client_id} " f"(queue_size: {max_queue_size})")
        return client_queue

    def remove_client(self, client_id: str):
        """
        Remove a client connection and log statistics.
        """
        with self.clients_lock:
            if client_id in self.connected_clients:
                client_info = self.connected_clients[client_id]

                # Log client statistics
                connection_duration = time.time() - client_info.connected_at
                logger.info(
                    f"SSE client disconnected: {client_id} "
                    f"(duration: {connection_duration:.1f}s, "
                    f"events_sent: {client_info.events_sent}, "
                    f"events_dropped: {client_info.events_dropped}, "
                    f"queue_full_count: {client_info.queue_full_count})"
                )

                del self.connected_clients[client_id]
            else:
                logger.debug(f"Attempted to remove non-existent client: {client_id}")

    def get_client_stats(self, client_id: str) -> Optional[dict]:
        """Get statistics for a specific client."""
        with self.clients_lock:
            if client_id in self.connected_clients:
                client_info = self.connected_clients[client_id]
                return {
                    "client_id": client_id,
                    "connected_at": client_info.connected_at,
                    "last_activity": client_info.last_activity,
                    "events_sent": client_info.events_sent,
                    "events_dropped": client_info.events_dropped,
                    "queue_full_count": client_info.queue_full_count,
                    "queue_size": client_info.client_queue.qsize(),
                    "max_queue_size": client_info.max_queue_size,
                }
        return None

    def get_all_client_stats(self) -> Dict[str, dict]:
        """Get statistics for all connected clients."""
        stats = {}
        with self.clients_lock:
            for client_id in self.connected_clients.keys():
                client_stats = self.get_client_stats(client_id)
                if client_stats:
                    stats[client_id] = client_stats
        return stats

    def send_update(self, event_name: str, event_type: str, data: Any, client_id: str = None):
        """
        Send an update to specific client with enhanced queue management.

        :param event_name: Name of the event
        :param event_type: Type of the event
        :param data: Event data
        :param client_id: Optional specific client ID to send to
        """
        event = {
            "event": event_name,
            "data": data,
            "type": event_type,
            "timestamp": time.time(),
            "client_id": client_id,
        }

        with self.clients_lock:
            # If client_id is specified, send to that specific client
            if client_id and client_id in self.connected_clients:
                client_info = self.connected_clients[client_id]
                client_info.last_activity = time.time()

                try:
                    client_info.client_queue.put(event, block=False)
                    client_info.events_sent += 1
                    logger.debug(f"Event sent to client: {client_id}")
                except queue.Full:
                    # Use enhanced queue management
                    success = self._handle_queue_full(client_info, event)
                    if success:
                        client_info.events_sent += 1
                        logger.info(f"Event queued for client {client_id} after ")
                    else:
                        client_info.events_dropped += 1
                        logger.warning(
                            f"Event dropped for client {client_id} - "
                            f"queue management failed "
                            f"(queue_size: "
                            f"{client_info.client_queue.qsize()}, "
                            f"max_size: {client_info.max_queue_size})"
                        )
            else:
                if client_id:
                    logger.warning(f"Client {client_id} not found for event delivery")

            # # If no client_id, broadcast to all clients
            # else:
            #     for client_queue in self.connected_clients.values():
            #         try:
            #             client_queue.put(event, block=False)
            #         except queue.Full:
            #             logger.warning("A client queue is full, skipping event")

    def clear_clients(self):
        """
        Clear all connected clients
        """
        with self.clients_lock:
            self.connected_clients.clear()
        logger.info("All clients cleared")

    def shutdown(self):
        """
        Graceful shutdown of SSE manager
        """
        self.is_shutting_down = True
        self.clear_clients()
        logger.info("SSE Manager shutting down")

    # Convenience methods for specific event types
    def send_session_initialized(self, session_id: str, client_id: str = None):
        """Send session initialized event."""
        self.send_update(
            event_name=SSEEventType.SESSION_INITIALIZED.value,
            event_type="session",
            data={"session_id": session_id, "status": "initialized"},
            client_id=client_id,
        )

    def send_message_stream_started(self, client_id: str = None):
        """Send message stream started event."""
        self.send_update(
            event_name=SSEEventType.MESSAGE_STREAM_STARTED.value,
            event_type="message",
            data={"status": "started"},
            client_id=client_id,
        )

    def send_message_response(
        self,
        content: str,
        content_type: ContentType = ContentType.TEXT_PLAIN,
        client_id: str = None,
    ):
        """Send message streaming chunk event."""
        data = {
            "content": content,
            "content_type": content_type,
        }

        self.send_update(
            event_name=SSEEventType.MESSAGE_RESPONSE.value,
            event_type="message",
            data=data,
            client_id=client_id,
        )

    def send_message_streaming(
        self,
        content: str,
        content_type: ContentType = ContentType.TEXT_PLAIN,
        message_type: str = "streaming_chunk",
        source: str = "assistant",
        metadata: Optional[Dict[str, Any]] = {},
        models_usage: Optional[Dict[str, Any]] = {},
        client_id: str = None,
    ):
        """Send message streaming chunk event."""
        data = {
            "content": content,
            "content_type": content_type,
            "message_type": message_type,
            "source": source,
            "models_usage": models_usage,
            "metadata": metadata,
        }

        self.send_update(
            event_name=SSEEventType.MESSAGE_STREAMING.value,
            event_type="message",
            data=data,
            client_id=client_id,
        )

    def send_message_end(self, session_id: str, client_id: str = None):
        """Send message end event."""
        self.send_update(
            event_name=SSEEventType.MESSAGE_END.value,
            event_type="message",
            data={"session_id": session_id, "status": "completed"},
            client_id=client_id,
        )

    def send_mcp_execution_started(
        self, data: Optional[Dict[str, Any]] = None, client_id: str = None
    ):
        """Send MCP execution started event."""
        self.send_update(
            event_name=SSEEventType.MCP_EXECUTION_STARTED.value,
            event_type="mcp",
            data=data,
            client_id=client_id,
        )

    def send_mcp_execution_ended(
        self, data: Optional[Dict[str, Any]] = None, client_id: str = None
    ):
        """Send MCP execution ended event."""

        self.send_update(
            event_name=SSEEventType.MCP_EXECUTION_ENDED.value,
            event_type="mcp",
            data=data,
            client_id=client_id,
        )

    def send_mcp_execution_failed(
        self,
        data: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send MCP execution failed event."""

        self.send_update(
            event_name=SSEEventType.MCP_EXECUTION_FAILED.value,
            event_type="mcp",
            data=data,
            client_id=client_id,
        )

    def send_workflow_execution_started(
        self,
        data: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send workflow execution started event."""
        self.send_update(
            event_name=SSEEventType.WORKFLOW_EXECUTION_STARTED.value,
            event_type="workflow",
            data=data,
            client_id=client_id,
        )

    def send_workflow_execution_step(
        self,
        workflow_id: str,
        correlation_id: str,
        step_id: str,
        step_name: str,
        step_status: str,
        result: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send workflow execution step event."""
        data = {
            "workflow_id": workflow_id,
            "correlation_id": correlation_id,
            "step_id": step_id,
            "step_name": step_name,
            "step_status": step_status,
        }
        if result:
            data["result"] = result

        self.send_update(
            event_name=SSEEventType.WORKFLOW_EXECUTION_STEP.value,
            event_type="workflow",
            data=data,
            client_id=client_id,
        )

    def send_workflow_execution_completed(
        self,
        workflow_id: str,
        correlation_id: str,
        result: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send workflow execution completed event."""
        data = {
            "workflow_id": workflow_id,
            "correlation_id": correlation_id,
            "status": "completed",
        }
        if result:
            data["result"] = result

        self.send_update(
            event_name=SSEEventType.WORKFLOW_EXECUTION_COMPLETED.value,
            event_type="workflow",
            data=data,
            client_id=client_id,
        )

    def send_workflow_execution_failed(
        self,
        data: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send workflow execution failed event."""

        self.send_update(
            event_name=SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
            event_type="workflow",
            data=data,
            client_id=client_id,
        )

    def send_knowledge_fetch_started(
        self, data: Optional[Dict[str, Any]] = None, client_id: str = None
    ):
        """Send knowledge fetch started event."""

        self.send_update(
            event_name=SSEEventType.KNOWLEDGE_FETCH_STARTED.value,
            event_type="knowledge",
            data=data,
            client_id=client_id,
        )

    def send_knowledge_fetch_completed(
        self,
        data: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send knowledge fetch completed event."""

        self.send_update(
            event_name=SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value,
            event_type="knowledge",
            data=data,
            client_id=client_id,
        )

    def send_knowledge_fetch_failed(
        self,
        data: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send knowledge fetch failed event."""

        self.send_update(
            event_name=SSEEventType.KNOWLEDGE_FETCH_FAILED.value,
            event_type="knowledge",
            data=data,
            client_id=client_id,
        )

    def send_error(
        self,
        error_code: str,
        error_message: str,
        details: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send error event."""
        data = {"error_code": error_code, "error_message": error_message}
        if details:
            data["details"] = details

        self.send_update(
            event_name=SSEEventType.ERROR.value,
            event_type="error",
            data=data,
            client_id=client_id,
        )

    def send_task_delegation_success(
        self,
        task_id: str,
        session_id: str,
        agent_session_id: str,
        conversation_id: str,
        agent_id: str,
        global_session_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        title: Optional[str] = None,
        client_id: str = None,
    ):
        """Send task delegation success event."""
        data = {
            "task_id": task_id,
            "session_id": session_id,
            "agent_session_id": agent_session_id,
            "conversation_id": conversation_id,
            "agent_id": agent_id,
            "status": "success",
        }

        if global_session_id:
            data["global_session_id"] = global_session_id
        if correlation_id:
            data["correlation_id"] = correlation_id
        if title:
            data["title"] = title

        self.send_update(
            event_name=SSEEventType.TASK_DELEGATION_SUCCESS.value,
            event_type="task_delegation",
            data=data,
            client_id=client_id,
        )

    def send_task_delegation_failed(
        self,
        error_code: str,
        error_message: str,
        correlation_id: Optional[str] = None,
        global_session_id: Optional[str] = None,
        client_id: str = None,
    ):
        """Send task delegation failed event."""
        data = {
            "error_code": error_code,
            "error_message": error_message,
            "status": "failed",
        }

        if correlation_id:
            data["correlation_id"] = correlation_id
        if global_session_id:
            data["global_session_id"] = global_session_id

        self.send_update(
            event_name=SSEEventType.TASK_DELEGATION_FAILED.value,
            event_type="task_delegation",
            data=data,
            client_id=client_id,
        )


sse_manager = SseManager()
